import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/power_bi_models.dart';
import '../../../models/chart_enums.dart';
import '../../../models/advanced_filter_options.dart';
import '../../../controllers/power_bi_controller.dart';
import '../charts/enhanced_bar_chart.dart';
import '../charts/enhanced_pie_chart.dart';
import '../charts/enhanced_line_chart.dart';

/// مكون المعاينة الفورية للرسوم البيانية
/// يعرض معاينة فورية للرسم البياني عند تغيير أي إعداد
class LivePreviewWidget extends StatefulWidget {
  final PowerBIChartType chartType;
  final String selectedTable;
  final List<String> selectedColumns;
  final String xAxisColumn;
  final String yAxisColumn;
  final String? sizeColumn;
  final String? colorColumn;
  final String? filterCriteria;
  final List<String>? relatedTables;
  final List<String>? joinConditions;
  final List<String>? joinTypes;
  final bool useMultipleTables;
  final VoidCallback? onRefresh;

  const LivePreviewWidget({
    super.key,
    required this.chartType,
    required this.selectedTable,
    required this.selectedColumns,
    required this.xAxisColumn,
    required this.yAxisColumn,
    this.sizeColumn,
    this.colorColumn,
    this.filterCriteria,
    this.relatedTables,
    this.joinConditions,
    this.joinTypes,
    this.useMultipleTables = false,
    this.onRefresh,
  });

  @override
  State<LivePreviewWidget> createState() => _LivePreviewWidgetState();
}

class _LivePreviewWidgetState extends State<LivePreviewWidget>
    with TickerProviderStateMixin {
  final PowerBIController _powerBIController = Get.find<PowerBIController>();
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  Map<String, dynamic> _previewData = {};
  bool _isGeneratingPreview = false;
  String _previewError = '';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _generatePreview();
  }

  @override
  void didUpdateWidget(LivePreviewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // إعادة توليد المعاينة عند تغيير أي إعداد
    if (_hasSettingsChanged(oldWidget)) {
      _generatePreview();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  bool _hasSettingsChanged(LivePreviewWidget oldWidget) {
    return oldWidget.chartType != widget.chartType ||
           oldWidget.selectedTable != widget.selectedTable ||
           !_listEquals(oldWidget.selectedColumns, widget.selectedColumns) ||
           oldWidget.xAxisColumn != widget.xAxisColumn ||
           oldWidget.yAxisColumn != widget.yAxisColumn ||
           oldWidget.sizeColumn != widget.sizeColumn ||
           oldWidget.colorColumn != widget.colorColumn ||
           oldWidget.filterCriteria != widget.filterCriteria ||
           !_listEquals(oldWidget.relatedTables, widget.relatedTables) ||
           !_listEquals(oldWidget.joinConditions, widget.joinConditions) ||
           !_listEquals(oldWidget.joinTypes, widget.joinTypes) ||
           oldWidget.useMultipleTables != widget.useMultipleTables;
  }

  bool _listEquals(List<String>? a, List<String>? b) {
    if (a == null && b == null) return true;
    if (a == null || b == null) return false;
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  Future<void> _generatePreview() async {
    if (!_canGeneratePreview()) {
      setState(() {
        _previewData = {};
        _previewError = '';
      });
      return;
    }

    setState(() {
      _isGeneratingPreview = true;
      _previewError = '';
    });

    try {
      // إنشاء تقرير مؤقت للمعاينة
      final tempReport = PowerBIReport(
        id: 'preview_${DateTime.now().millisecondsSinceEpoch}',
        title: 'معاينة فورية',
        chartType: widget.chartType,
        createdById: 'preview_user',
        createdAt: DateTime.now(),
        tableName: widget.selectedTable,
        columnNames: widget.selectedColumns,
        xAxisColumn: widget.xAxisColumn,
        yAxisColumn: widget.yAxisColumn,
        sizeColumn: widget.sizeColumn,
        colorColumn: widget.colorColumn,
        filterCriteria: widget.filterCriteria,
        relatedTables: widget.relatedTables,
        joinConditions: widget.joinConditions,
        joinTypes: widget.joinTypes,
      );

      // الحصول على بيانات المعاينة
      final data = await _powerBIController.getChartData(tempReport);
      
      setState(() {
        _previewData = data;
        _isGeneratingPreview = false;
      });

      // تشغيل الرسوم المتحركة
      _animationController.forward();

    } catch (e) {
      setState(() {
        _previewError = 'خطأ في توليد المعاينة: ${e.toString()}';
        _isGeneratingPreview = false;
      });
    }
  }

  bool _canGeneratePreview() {
    return widget.selectedTable.isNotEmpty &&
           widget.selectedColumns.isNotEmpty &&
           widget.xAxisColumn.isNotEmpty &&
           widget.yAxisColumn.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2E5BFF).withValues(alpha: 0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF2E5BFF),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.preview,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'معاينة فورية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2E384D),
              ),
            ),
          ),
          if (widget.onRefresh != null)
            IconButton(
              onPressed: widget.onRefresh,
              icon: const Icon(Icons.refresh),
              tooltip: 'تحديث المعاينة',
            ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (!_canGeneratePreview()) {
      return _buildEmptyState();
    }

    if (_isGeneratingPreview) {
      return _buildLoadingState();
    }

    if (_previewError.isNotEmpty) {
      return _buildErrorState();
    }

    if (_previewData.isEmpty) {
      return _buildNoDataState();
    }

    return _buildChart();
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'اختر البيانات لعرض المعاينة',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قم بتحديد الجدول والأعمدة لرؤية معاينة فورية للرسم البياني',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 60,
            height: 60,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(
                const Color(0xFF2E5BFF),
              ),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'جاري إنشاء المعاينة...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF2E384D),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'يتم تحليل البيانات وإنشاء الرسم البياني',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red.shade400,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في المعاينة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _previewError,
              style: TextStyle(
                fontSize: 14,
                color: Colors.red.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _generatePreview,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade400,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.data_usage_outlined,
            size: 64,
            color: Colors.orange.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات للعرض',
            style: TextStyle(
              fontSize: 16,
              color: Colors.orange.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تأكد من صحة إعدادات الجدول والأعمدة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.orange.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChart() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: _buildChartByType(),
        ),
      ),
    );
  }

  Widget _buildChartByType() {
    switch (widget.chartType) {
      case PowerBIChartType.bar:
        final data = _previewData['chartData'] as Map<String, double>? ?? {};
        return EnhancedBarChart(
          data: data,
          title: '',
          xAxisTitle: widget.xAxisColumn,
          yAxisTitle: widget.yAxisColumn,
          showGrid: true,
          showValues: true,
          chartType: ChartType.bar,
          advancedFilterOptions: const AdvancedFilterOptions(),
        );

      case PowerBIChartType.pie:
        final data = _previewData['chartData'] as Map<String, double>? ?? {};
        return EnhancedPieChart(
          data: data,
          title: '',
          showValues: true,
          showLegend: true,
          chartType: ChartType.pie,
          advancedFilterOptions: const AdvancedFilterOptions(),
        );

      case PowerBIChartType.line:
        final rawData = _previewData['chartData'] as Map<String, dynamic>? ?? {};
        final Map<String, Map<String, double>> data = {};
        
        for (final entry in rawData.entries) {
          if (entry.value is List) {
            final Map<String, double> seriesData = {};
            final List<dynamic> spots = entry.value;
            
            for (int i = 0; i < spots.length; i++) {
              if (spots[i] is Map && spots[i].containsKey('x') && spots[i].containsKey('y')) {
                seriesData[spots[i]['x'].toString()] = spots[i]['y'].toDouble();
              } else {
                seriesData[i.toString()] = spots[i].toDouble();
              }
            }
            
            data[entry.key] = seriesData;
          }
        }
        
        return EnhancedLineChart(
          data: data,
          title: '',
          xAxisTitle: widget.xAxisColumn,
          yAxisTitle: widget.yAxisColumn,
          showGrid: true,
          showDots: true,
          chartType: ChartType.line,
          advancedFilterOptions: const AdvancedFilterOptions(),
        );

      default:
        return Center(
          child: Text(
            'نوع الرسم البياني "${widget.chartType.displayName}" غير مدعوم في المعاينة',
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
        );
    }
  }
}
