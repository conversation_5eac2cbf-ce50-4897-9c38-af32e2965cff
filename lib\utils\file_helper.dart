import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:cross_file/cross_file.dart';
import 'web_file_stub.dart' if (dart.library.io) 'dart:io';

/// مساعد للتعامل مع الملفات بطريقة متوافقة مع جميع المنصات
/// يوفر طرقًا للتعامل مع الملفات بشكل موحد بين الويب والتطبيقات المحلية
class FileHelper {
  /// تحويل File إلى XFile
  /// يستخدم للتوافق بين المنصات المختلفة
  static XFile fileToXFile(dynamic file) {
    if (kIsWeb) {
      return XFile(file.path);
    } else {
      return XFile(file.path);
    }
  }

  /// تحويل XFile إلى File
  /// يستخدم للتوافق بين المنصات المختلفة
  static dynamic xFileToFile(XFile xFile) {
    if (kIsWeb) {
      return WebFile(xFile.path);
    } else {
      return File(xFile.path);
    }
  }

  /// تحويل List<int> إلى Uint8List
  /// يستخدم لحل مشكلة التوافق بين List<int> و Uint8List
  static Uint8List listToUint8List(List<int> data) {
    return Uint8List.fromList(data);
  }

  /// إنشاء ملف من المسار
  /// يتعامل مع الاختلافات بين الويب والتطبيقات المحلية
  static dynamic createFileFromPath(String path) {
    if (kIsWeb) {
      return WebFile(path);
    } else {
      return File(path);
    }
  }

  /// التحقق مما إذا كان التطبيق يعمل على الويب
  static bool get isWeb => kIsWeb;

  /// الحصول على اسم الملف من المسار
  static String getFileNameFromPath(String path) {
    return path.split('/').last;
  }

  /// الحصول على امتداد الملف من المسار
  static String getFileExtension(String path) {
    return path.split('.').last.toLowerCase();
  }

  /// تحويل قائمة WebFile إلى قائمة File للتوافق
  static List<dynamic> convertFileList(List<dynamic> files) {
    if (kIsWeb) {
      return files; // على الويب، نعيد القائمة كما هي
    } else {
      return files.map((file) => file is WebFile ? File(file.path) : file).toList();
    }
  }

  /// تحويل ملف واحد للتوافق
  static dynamic convertFile(dynamic file) {
    if (kIsWeb) {
      return file; // على الويب، نعيد الملف كما هو
    } else {
      return file is WebFile ? File(file.path) : file;
    }
  }
}
