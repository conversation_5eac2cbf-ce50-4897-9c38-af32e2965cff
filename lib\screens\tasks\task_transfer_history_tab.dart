import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:timeline_tile/timeline_tile.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/task_history_models.dart';
import '../../models/task_action_type.dart';
import '../widgets/task_transfer_history_widget.dart';

/// تبويب تاريخ تحويلات المهمة
/// يعرض تاريخ تحويلات المهمة بشكل مرئي مع مخطط زمني
class TaskTransferHistoryTab extends StatelessWidget {
  final String taskId;

  const TaskTransferHistoryTab({
    super.key,
    required this.taskId,
  });

  @override
  Widget build(BuildContext context) {
    // final taskController = Get.find<TaskController>(); // غير مستخدم

    // استبدال Obx بـ GetBuilder لتجنب تحديث الحالة أثناء البناء
    return GetBuilder<TaskController>(
      builder: (controller) {
        final transferHistory = controller.taskHistory
            .where((history) => history.actionType == TaskActionType.transferred)
            .toList();

        if (transferHistory.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.textSecondary.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.swap_horiz,
                    size: 64,
                    color: AppColors.textSecondary.withValues(alpha: 0.6),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'لا يوجد تاريخ تحويلات لهذه المهمة',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'ستظهر هنا جميع تحويلات المهمة عند حدوثها',
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary.withValues(alpha: 0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان التبويب
              Text(
                'تاريخ تحويلات المهمة',
                style: AppStyles.headingMedium,
              ),
              const SizedBox(height: 24),

              // إحصائيات التحويلات
              _buildTransferStats(transferHistory),
              const SizedBox(height: 24),

              // المخطط الزمني للتحويلات
              _buildTransferTimeline(transferHistory),
              const SizedBox(height: 24),

              // تفاصيل التحويلات
              Text(
                'تفاصيل التحويلات',
                style: AppStyles.titleMedium,
              ),
              const SizedBox(height: 16),
              TaskTransferHistoryWidget(
                transferHistory: transferHistory,
                showTitle: false,
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء إحصائيات التحويلات
  Widget _buildTransferStats(List<TaskHistory> transferHistory) {
    // حساب إجمالي المساهمات المسجلة
    double totalContributions = 0.0;
    int contributionsCount = 0;
    int totalAttachments = 0;
    int totalWithNotes = 0;

    for (var history in transferHistory) {
      // تحويل details من String إلى Map إذا لزم الأمر
      Map<String, dynamic>? detailsMap;
      if (history.details != null) {
        try {
          // إذا كان details عبارة عن JSON string
          if (history.details!.startsWith('{')) {
            detailsMap = json.decode(history.details!) as Map<String, dynamic>;
          }
        } catch (e) {
          // إذا فشل التحويل، نتجاهل هذا السجل
          debugPrint('خطأ في تحليل تفاصيل التحويل: $e');
          continue;
        }
      }

      // حساب المساهمات
      if (detailsMap != null &&
          detailsMap.containsKey('contributionRecorded') &&
          detailsMap['contributionRecorded'] == 'true' &&
          detailsMap.containsKey('contributionPercentage')) {
        try {
          totalContributions +=
              double.parse(detailsMap['contributionPercentage'].toString());
          contributionsCount++;
        } catch (e) {
          debugPrint('خطأ في تحليل نسبة المساهمة: $e');
        }
      }
      
      // حساب المرفقات
      if (detailsMap != null && 
          detailsMap.containsKey('attachments') && 
          detailsMap['attachments'].toString().isNotEmpty) {
        final attachmentsList = detailsMap['attachments'].toString().split(',');
        if (attachmentsList.isNotEmpty && attachmentsList.first.isNotEmpty) {
          totalAttachments += attachmentsList.length;
        }
      }
      
      // حساب التعليقات
      if (detailsMap != null && 
          detailsMap.containsKey('note') && 
          detailsMap['note'].toString().isNotEmpty) {
        totalWithNotes++;
      }
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات التحويلات',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'عدد التحويلات',
                    transferHistory.length.toString(),
                    Icons.swap_horiz,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'آخر تحويل',
                    transferHistory.isNotEmpty
                        ? DateFormat('yyyy/MM/dd').format(
                            DateTime.fromMillisecondsSinceEpoch(
                              transferHistory
                                  .map((h) => h.timestamp)
                                  .reduce((a, b) => a > b ? a : b) * 1000,
                            ),
                          )
                        : 'لا يوجد',
                    Icons.calendar_today,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'تحويلات بتعليقات',
                    totalWithNotes.toString(),
                    Icons.comment,
                    color: AppColors.info,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'عدد المرفقات',
                    totalAttachments.toString(),
                    Icons.attach_file,
                    color: AppColors.accent,
                  ),
                ),
              ],
            ),
            if (contributionsCount > 0) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'المساهمات المسجلة',
                      contributionsCount.toString(),
                      Icons.star,
                      color: AppColors.warning,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'إجمالي المساهمات',
                      '${totalContributions.toStringAsFixed(1)}%',
                      Icons.pie_chart,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon,
      {Color? color}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.border.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: color ?? AppColors.primary,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: AppStyles.labelMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppStyles.titleLarge.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء المخطط الزمني للتحويلات
  Widget _buildTransferTimeline(List<TaskHistory> transferHistory) {
    // ترتيب التحويلات من الأقدم إلى الأحدث
    final sortedHistory = List<TaskHistory>.from(transferHistory)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المخطط الزمني للتحويلات',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: sortedHistory.length,
              itemBuilder: (context, index) {
                final history = sortedHistory[index];
                final isFirst = index == 0;
                final isLast = index == sortedHistory.length - 1;

                return TimelineTile(
                  alignment: TimelineAlign.manual,
                  lineXY: 0.2,
                  isFirst: isFirst,
                  isLast: isLast,
                  indicatorStyle: IndicatorStyle(
                    width: 20,
                    height: 20,
                    indicator: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.primary,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.3),
                            blurRadius: 6,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.swap_horiz,
                        size: 12,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  beforeLineStyle: LineStyle(
                    color: AppColors.primary.withValues(alpha: 0.5),
                    thickness: 2,
                  ),
                  afterLineStyle: LineStyle(
                    color: AppColors.primary.withValues(alpha: 0.5),
                    thickness: 2,
                  ),
                  startChild: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      DateFormat('yyyy/MM/dd\nHH:mm').format(
                        DateTime.fromMillisecondsSinceEpoch(history.timestamp * 1000),
                      ),
                      style: AppStyles.captionText.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  endChild: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            FutureBuilder<String>(
                              future: Get.find<UserController>()
                                  .getUserNameById(history.userId.toString()),
                              builder: (context, snapshot) {
                                return Text(
                                  snapshot.data ?? 'مستخدم غير معروف',
                                  style: AppStyles.bodyMedium.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.textPrimary,
                                  ),
                                );
                              },
                            ),
                            Icon(
                              Icons.arrow_forward,
                              color: AppColors.textSecondary,
                              size: 20,
                            ),
                            FutureBuilder<String>(
                              future: _getNewAssigneeId(history).then((id) =>
                                  Get.find<UserController>().getUserNameById(id)),
                              builder: (context, snapshot) {
                                return Text(
                                  snapshot.data ?? 'مستخدم غير معروف',
                                  style: AppStyles.bodyMedium.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primary,
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                        // عرض نسبة المساهمة إذا كانت موجودة
                        if (_hasContribution(history))
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.star,
                                  color: AppColors.warning,
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'المساهمة: ${_getContributionPercentage(history)}%',
                                  style: AppStyles.captionText.copyWith(
                                    color: AppColors.warning,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        if (_hasNote(history))
                          Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: AppColors.surface.withValues(alpha: 0.8),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: AppColors.border.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                _getNote(history),
                                style: AppStyles.bodySmall.copyWith(
                                  color: AppColors.textPrimary,
                                  height: 1.4,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// دوال مساعدة للتعامل مع details
  Future<String> _getNewAssigneeId(TaskHistory history) async {
    if (history.details == null) {
      // استخدام newValue كبديل إذا كان متوفراً
      return history.newValue ?? 'غير محدد';
    }

    try {
      // محاولة تحليل details كـ JSON
      if (history.details!.startsWith('{')) {
        final detailsMap = json.decode(history.details!) as Map<String, dynamic>;
        final newAssigneeId = detailsMap['newAssigneeId']?.toString();

        // التحقق من صحة المعرف
        if (newAssigneeId != null && newAssigneeId != '0' && newAssigneeId.isNotEmpty) {
          return newAssigneeId;
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحليل details: $e');
    }

    // استخدام newValue كبديل
    if (history.newValue != null && history.newValue != '0' && history.newValue!.isNotEmpty) {
      return history.newValue!;
    }

    return 'غير محدد';
  }

  bool _hasContribution(TaskHistory history) {
    if (history.details == null) return false;

    try {
      if (history.details!.startsWith('{')) {
        final detailsMap = json.decode(history.details!) as Map<String, dynamic>;
        return detailsMap.containsKey('contributionRecorded') &&
               detailsMap['contributionRecorded'] == 'true' &&
               detailsMap.containsKey('contributionPercentage');
      }
    } catch (e) {
      debugPrint('خطأ في تحليل المساهمة: $e');
    }
    return false;
  }

  String _getContributionPercentage(TaskHistory history) {
    if (history.details == null) return '0.0';

    try {
      if (history.details!.startsWith('{')) {
        final detailsMap = json.decode(history.details!) as Map<String, dynamic>;
        final percentage = detailsMap['contributionPercentage']?.toString() ?? '0.0';
        return double.parse(percentage).toStringAsFixed(1);
      }
    } catch (e) {
      debugPrint('خطأ في تحليل نسبة المساهمة: $e');
    }
    return '0.0';
  }

  bool _hasNote(TaskHistory history) {
    if (history.details == null) return false;

    try {
      if (history.details!.startsWith('{')) {
        final detailsMap = json.decode(history.details!) as Map<String, dynamic>;
        final note = detailsMap['note']?.toString();
        return note != null && note.isNotEmpty;
      }
    } catch (e) {
      // تجاهل الأخطاء
    }
    return false;
  }

  String _getNote(TaskHistory history) {
    if (history.details == null) return '';

    try {
      if (history.details!.startsWith('{')) {
        final detailsMap = json.decode(history.details!) as Map<String, dynamic>;
        return detailsMap['note']?.toString() ?? '';
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على الملاحظة: $e');
    }
    return '';
  }
}
