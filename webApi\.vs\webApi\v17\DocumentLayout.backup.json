{"Version": 1, "WorkspaceRootPath": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.roles.sql||{0058A1F7-65F3-4DB9-B3D0-CA7E64DD73CD}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 398, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "dbo.roles.sql", "DocumentMoniker": "ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.roles.sql", "ToolTip": "ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.roles.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-23T23:11:21.883Z"}]}]}]}