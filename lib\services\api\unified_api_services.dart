/// ملف تصدير موحد لجميع خدمات API
library;
/// يسهل استيراد وإدارة جميع خدمات API من مكان واحد
/// 
/// تم توحيد وتحسين جميع الخدمات لتجنب التضارب والتكرار

// ===== الخدمات الأساسية =====
export 'api_service.dart';
export 'base_api_service.dart';

// ===== خدمات المصادقة والمستخدمين =====
export 'auth_api_service.dart';
export 'users_api_service.dart'; // الخدمة الموحدة للمستخدمين

// ===== خدمات الصلاحيات والأدوار =====
export 'permissions_api_service.dart'; // الخدمة الموحدة للصلاحيات
export 'user_permissions_api_service.dart';
export 'role_api_service.dart';

// ===== خدمات الأقسام والتنظيم =====
export 'departments_api_service.dart'; // الخدمة الموحدة للأقسام

// ===== خدمات المهام =====
export 'task_api_service.dart';
export 'task_comments_api_service.dart';
export 'task_documents_api_service.dart';
export 'task_history_api_service.dart';
export 'task_messages_api_service.dart';
export 'task_priority_api_service.dart';
export 'task_progress_trackers_api_service.dart';
export 'task_types_api_service.dart';
export 'task_access_api_service.dart';
export 'subtasks_api_service.dart';
export 'time_tracking_api_service.dart';

// ===== خدمات الرسائل والدردشة =====
export 'messages_api_service.dart';
export 'chat_groups_api_service.dart';
export 'group_members_api_service.dart';
export 'message_attachments_api_service.dart';
export 'message_reactions_api_service.dart';
export 'message_reads_api_service.dart';

// ===== خدمات الإشعارات =====
export 'notifications_api_service.dart';
export 'notification_settings_api_service.dart';

// ===== خدمات الأرشيف والمستندات =====
export 'archive_documents_api_service.dart';
export 'archive_categories_api_service.dart';
export 'archive_tags_api_service.dart';
export 'archive_document_tags_api_service.dart';
export 'attachments_api_service.dart';
export 'unified_document_api_service.dart';
export 'text_document_api_service.dart';

// ===== خدمات التقارير والتحليلات =====
export 'reports_api_service.dart';
export 'report_schedules_api_service.dart';
export 'contribution_reports_api_service.dart';
export 'power_bi_api_service.dart';

// ===== خدمات النظام والإدارة =====
export 'system_logs_api_service.dart';
export 'system_settings_api_service.dart';
export 'system_statistics_api_service.dart';
export 'activity_logs_api_service.dart';
export 'backups_api_service.dart'; // الخدمة الموحدة للنسخ الاحتياطية

// ===== خدمات التقويم واللوحات =====
export 'calendar_events_api_service.dart';


// ===== خدمات التحميل والرفع =====
export 'image_upload_api_service.dart';

// ===== خدمات البيانات التجريبية والاختبار =====
export 'seed_data_api_service.dart';
export 'test_api_service.dart';

// ===== خدمات أخرى =====

export 'simple_task_comments_api_service.dart';

/// فئة مساعدة لتهيئة جميع خدمات API
class UnifiedApiServices {
  /// قائمة بجميع خدمات API المتاحة
  static const List<String> availableServices = [
    'AuthApiService',
    'UsersApiService',
    'PermissionsApiService',
    'DepartmentsApiService',
    'TaskApiService',
    'MessagesApiService',
    'NotificationsApiService',
    'ReportsApiService',
    'SystemLogsApiService',
    'BackupsApiService',
    'CalendarEventsApiService',
    'ArchiveDocumentsApiService',
  ];

  /// التحقق من توفر خدمة API معينة
  static bool isServiceAvailable(String serviceName) {
    return availableServices.contains(serviceName);
  }

  /// الحصول على قائمة بجميع الخدمات المتاحة
  static List<String> getAllServices() {
    return List.from(availableServices);
  }

  /// الحصول على معلومات حول الخدمات الموحدة
  static Map<String, String> getUnifiedServicesInfo() {
    return {
      'users': 'UsersApiService - خدمة موحدة للمستخدمين',
      'permissions': 'PermissionsApiService - خدمة موحدة للصلاحيات',
      'departments': 'DepartmentsApiService - خدمة موحدة للأقسام',
      'backups': 'BackupsApiService - خدمة موحدة للنسخ الاحتياطية',
      'total_services': '${availableServices.length} خدمة متاحة',
      'unified_count': '4 خدمات تم توحيدها',
    };
  }
}

/// تعداد لأنواع خدمات API المختلفة
enum ApiServiceType {
  authentication,
  users,
  permissions,
  tasks,
  messages,
  notifications,
  reports,
  system,
  documents,
  calendar,
}

/// فئة مساعدة لتصنيف خدمات API
class ApiServiceClassifier {
  /// تصنيف الخدمات حسب النوع
  static Map<ApiServiceType, List<String>> classifyServices() {
    return {
      ApiServiceType.authentication: ['AuthApiService'],
      ApiServiceType.users: ['UsersApiService', 'UserPermissionsApiService'],
      ApiServiceType.permissions: ['PermissionsApiService', 'RoleApiService'],
      ApiServiceType.tasks: [
        'TaskApiService',
        'TaskCommentsApiService',
        'TaskDocumentsApiService',
        'TaskHistoryApiService',
        'SubtasksApiService',
      ],
      ApiServiceType.messages: [
        'MessagesApiService',
        'ChatGroupsApiService',
        'MessageAttachmentsApiService',
      ],
      ApiServiceType.notifications: [
        'NotificationsApiService',
        'NotificationSettingsApiService',
      ],
      ApiServiceType.reports: [
        'ReportsApiService',
        'ReportSchedulesApiService',
        'PowerBiApiService',
      ],
      ApiServiceType.system: [
        'SystemLogsApiService',
        'SystemSettingsApiService',
        'BackupsApiService',
        'ActivityLogsApiService',
      ],
      ApiServiceType.documents: [
        'ArchiveDocumentsApiService',
        'AttachmentsApiService',
        'UnifiedDocumentApiService',
      ],
      ApiServiceType.calendar: ['CalendarEventsApiService'],
    };
  }

  /// الحصول على خدمات نوع معين
  static List<String> getServicesByType(ApiServiceType type) {
    final classified = classifyServices();
    return classified[type] ?? [];
  }
}
