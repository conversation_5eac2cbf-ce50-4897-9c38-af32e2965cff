import 'package:flutter/material.dart';

/// معالج الأخطاء الموحد لمحرر Quill
/// يوفر معالجة موحدة ومبسطة للأخطاء مع رسائل واضحة للمستخدم
class QuillErrorHandler {
  /// تسجيل وعرض خطأ عام
  static void handleError(
    BuildContext? context,
    String operation,
    dynamic error, {
    StackTrace? stackTrace,
    bool showToUser = true,
  }) {
    // تسجيل الخطأ في وحدة التحكم
    debugPrint('❌ خطأ في $operation: $error');
    if (stackTrace != null) {
      debugPrint('📍 Stack trace: $stackTrace');
    }

    // عرض رسالة للمستخدم إذا كان مطلوباً
    if (showToUser && context != null) {
      _showErrorToUser(context, operation, error);
    }
  }

  /// معالجة أخطاء تصدير PDF
  static void handlePdfError(
    BuildContext? context,
    dynamic error, {
    StackTrace? stackTrace,
  }) {
    handleError(
      context,
      'تصدير PDF',
      error,
      stackTrace: stackTrace,
      showToUser: true,
    );
  }

  /// معالجة أخطاء رفع الملفات
  static void handleUploadError(
    BuildContext? context,
    dynamic error, {
    StackTrace? stackTrace,
  }) {
    handleError(
      context,
      'رفع الملف',
      error,
      stackTrace: stackTrace,
      showToUser: true,
    );
  }

  /// معالجة أخطاء الحفظ التلقائي
  static void handleAutoSaveError(
    BuildContext? context,
    dynamic error, {
    bool showToUser = false,
  }) {
    handleError(
      context,
      'الحفظ التلقائي',
      error,
      showToUser: showToUser,
    );
  }

  /// معالجة أخطاء إدراج الجداول
  static void handleTableError(
    BuildContext? context,
    dynamic error, {
    StackTrace? stackTrace,
  }) {
    handleError(
      context,
      'إدراج الجدول',
      error,
      stackTrace: stackTrace,
      showToUser: true,
    );
  }

  /// معالجة أخطاء إدراج الصور
  static void handleImageError(
    BuildContext? context,
    dynamic error, {
    StackTrace? stackTrace,
  }) {
    handleError(
      context,
      'إدراج الصورة',
      error,
      stackTrace: stackTrace,
      showToUser: true,
    );
  }

  /// معالجة أخطاء البحث والاستبدال
  static void handleSearchError(
    BuildContext? context,
    dynamic error,
  ) {
    handleError(
      context,
      'البحث والاستبدال',
      error,
      showToUser: true,
    );
  }

  /// عرض رسالة خطأ للمستخدم
  static void _showErrorToUser(
    BuildContext context,
    String operation,
    dynamic error,
  ) {
    try {
      final errorMessage = _getSimplifiedErrorMessage(operation, error);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'إغلاق',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    } catch (e) {
      debugPrint('خطأ في عرض رسالة الخطأ للمستخدم: $e');
    }
  }

  /// تبسيط رسالة الخطأ للمستخدم
  static String _getSimplifiedErrorMessage(String operation, dynamic error) {
    final errorString = error.toString().toLowerCase();

    // أخطاء الشبكة
    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return 'خطأ في الاتصال بالشبكة. تحقق من اتصال الإنترنت وحاول مرة أخرى.';
    }

    // أخطاء الملفات
    if (errorString.contains('file') || 
        errorString.contains('path') ||
        errorString.contains('permission')) {
      return 'خطأ في الوصول إلى الملف. تحقق من الصلاحيات وحاول مرة أخرى.';
    }

    // أخطاء الذاكرة
    if (errorString.contains('memory') || 
        errorString.contains('out of memory')) {
      return 'الملف كبير جداً. حاول تقليل حجم المحتوى أو إعادة تشغيل التطبيق.';
    }

    // أخطاء التنسيق
    if (errorString.contains('format') || 
        errorString.contains('parse') ||
        errorString.contains('json')) {
      return 'خطأ في تنسيق البيانات. حاول إعادة تحميل المستند.';
    }

    // أخطاء الصلاحيات
    if (errorString.contains('permission') || 
        errorString.contains('unauthorized') ||
        errorString.contains('forbidden')) {
      return 'ليس لديك صلاحية لتنفيذ هذا الإجراء.';
    }

    // رسالة عامة حسب نوع العملية
    switch (operation) {
      case 'تصدير PDF':
        return 'فشل في تصدير PDF. حاول مرة أخرى أو قلل من حجم المحتوى.';
      case 'رفع الملف':
        return 'فشل في رفع الملف. تحقق من حجم الملف واتصال الإنترنت.';
      case 'إدراج الجدول':
        return 'فشل في إدراج الجدول. حاول مرة أخرى.';
      case 'إدراج الصورة':
        return 'فشل في إدراج الصورة. تحقق من صيغة الصورة وحجمها.';
      case 'البحث والاستبدال':
        return 'فشل في عملية البحث والاستبدال. حاول مرة أخرى.';
      case 'الحفظ التلقائي':
        return 'فشل في الحفظ التلقائي. تحقق من اتصال الإنترنت.';
      default:
        return 'حدث خطأ في $operation. حاول مرة أخرى.';
    }
  }

  /// عرض رسالة نجاح
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: duration,
        ),
      );
    } catch (e) {
      debugPrint('خطأ في عرض رسالة النجاح: $e');
    }
  }

  /// عرض رسالة تحذير
  static void showWarning(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
  }) {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.orange,
          duration: duration,
        ),
      );
    } catch (e) {
      debugPrint('خطأ في عرض رسالة التحذير: $e');
    }
  }

  /// عرض حوار تحذير للنصوص الطويلة
  static Future<bool> showLongTextWarning(
    BuildContext context,
    int textLength,
  ) async {
    try {
      final result = await showDialog<bool>(
        context: context,
        builder: (context) => _LongTextWarningDialog(textLength: textLength),
      );
      return result ?? false;
    } catch (e) {
      debugPrint('خطأ في عرض تحذير النص الطويل: $e');
      return false;
    }
  }

  /// عرض حوار التحميل
  static void showLoadingDialog(
    BuildContext context,
    String message,
  ) {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => _LoadingDialog(message: message),
      );
    } catch (e) {
      debugPrint('خطأ في عرض حوار التحميل: $e');
    }
  }

  /// إخفاء حوار التحميل
  static void hideLoadingDialog(BuildContext context) {
    try {
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('خطأ في إخفاء حوار التحميل: $e');
    }
  }
}

/// حوار تحذير النص الطويل مع أيقونة تكبير الشاشة
class _LongTextWarningDialog extends StatefulWidget {
  final int textLength;

  const _LongTextWarningDialog({required this.textLength});

  @override
  State<_LongTextWarningDialog> createState() => _LongTextWarningDialogState();
}

class _LongTextWarningDialogState extends State<_LongTextWarningDialog> {
  bool _isFullscreen = false;

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Dialog(
      child: Container(
        width: _isFullscreen ? screenSize.width * 0.95 : screenSize.width * 0.5,
        height: _isFullscreen ? screenSize.height * 0.95 : null,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: _isFullscreen ? MainAxisSize.max : MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.orange),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تحذير: نص طويل',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                // أيقونة تكبير/تصغير الشاشة
                IconButton(
                  onPressed: _toggleFullscreen,
                  icon: Icon(_isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen),
                  tooltip: _isFullscreen ? 'تصغير الشاشة' : 'تكبير الشاشة',
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // المحتوى
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('النص طويل جداً (${widget.textLength.toString()} حرف).'),
                  const SizedBox(height: 8),
                  const Text('قد تستغرق العملية وقتاً أطول من المعتاد.'),
                  const SizedBox(height: 8),
                  const Text('هل تريد المتابعة؟'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // الأزرار
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('متابعة'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// حوار التحميل مع أيقونة تكبير الشاشة
class _LoadingDialog extends StatefulWidget {
  final String message;

  const _LoadingDialog({required this.message});

  @override
  State<_LoadingDialog> createState() => _LoadingDialogState();
}

class _LoadingDialogState extends State<_LoadingDialog> {
  bool _isFullscreen = false;

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Dialog(
      child: Container(
        width: _isFullscreen ? screenSize.width * 0.95 : screenSize.width * 0.4,
        height: _isFullscreen ? screenSize.height * 0.95 : null,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: _isFullscreen ? MainAxisSize.max : MainAxisSize.min,
          children: [
            // العنوان مع أيقونة تكبير الشاشة
            Row(
              children: [
                const Expanded(
                  child: Text(
                    'جاري التحميل',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                // أيقونة تكبير/تصغير الشاشة
                IconButton(
                  onPressed: _toggleFullscreen,
                  icon: Icon(_isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen),
                  tooltip: _isFullscreen ? 'تصغير الشاشة' : 'تكبير الشاشة',
                ),
              ],
            ),
            const SizedBox(height: 16),
            // المحتوى
            Expanded(
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        widget.message,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
