import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../controllers/auth_controller.dart';
import '../../../models/user_model.dart';
import '../../../models/permission_models.dart';
import '../../../models/user_permission_model.dart';
import '../../../models/role_model.dart';
import '../../../services/api/permissions_api_service.dart';
import '../../../services/api/user_permissions_api_service.dart';
import '../../../services/api/roles_api_service.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_dialog_widget.dart';

/// حوار إدارة صلاحيات المستخدم
class UserPermissionsDialog extends StatefulWidget {
  final User user;

  const UserPermissionsDialog({super.key, required this.user});

  @override
  State<UserPermissionsDialog> createState() => _UserPermissionsDialogState();
}

class _UserPermissionsDialogState extends State<UserPermissionsDialog> {
  final AdminController _adminController = Get.find<AdminController>();
  final AuthController _authController = Get.find<AuthController>();
  final PermissionsApiService _permissionsApiService = PermissionsApiService();
  final UserPermissionsApiService _userPermissionsApiService = UserPermissionsApiService();
  final RolesApiService _rolesApiService = RolesApiService();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  final RxBool _isLoading = false.obs;
  final RxBool _isSaving = false.obs;
  final RxMap<int, bool> _userPermissions = <int, bool>{}.obs;
  final RxList<UserPermission> _customPermissions = <UserPermission>[].obs;
  final RxString _searchQuery = ''.obs;
  final RxList<Role> _roles = <Role>[].obs;
  final RxBool _isExpanded = false.obs;
  final TextEditingController _searchController = TextEditingController();

  /// تحويل نص الأيقونة إلى IconData ثابت
  IconData _getIconFromString(String? iconString) {
    if (iconString == null || iconString.isEmpty) {
      return Icons.security;
    }

    final iconCode = int.tryParse(iconString);
    if (iconCode == null) {
      return Icons.security;
    }

    return IconData(iconCode, fontFamily: 'MaterialIcons');
  }

  @override
  void initState() {
    super.initState();
    _loadUserPermissions();
    _loadRoles();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل صلاحيات المستخدم
  Future<void> _loadUserPermissions() async {
    _isLoading.value = true;
    try {
      // تحميل الصلاحيات المخصصة للمستخدم
      final customPermissions = await _permissionsApiService.getCustomUserPermissions(widget.user.id);
      _customPermissions.assignAll(customPermissions);
      
      // تحويل الصلاحيات المخصصة إلى خريطة
      for (final customPerm in customPermissions) {
        _userPermissions[customPerm.permissionId] = customPerm.isActive;
      }
      
      debugPrint('✅ تم تحميل ${customPermissions.length} صلاحية مخصصة للمستخدم ${widget.user.name}');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل صلاحيات المستخدم: $e');
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل صلاحيات المستخدم: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الأدوار من قاعدة البيانات
  Future<void> _loadRoles() async {
    try {
      final roles = _adminController.roles;
      _roles.assignAll(roles);
      debugPrint('✅ تم تحميل ${roles.length} دور');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأدوار: $e');
    }
  }

  /// حفظ تغييرات الصلاحيات
  Future<void> _savePermissions() async {
    _isSaving.value = true;
    try {
      debugPrint('🚀 بدء حفظ تغييرات الصلاحيات للمستخدم ${widget.user.id}');

      // إضافة الصلاحيات الجديدة المفعلة
      for (final entry in _userPermissions.entries) {
        if (entry.value) { // فقط إضافة الصلاحيات المفعلة
          // التحقق من أن الصلاحية ليست موجودة في الصلاحيات المخصصة الحالية
          final isAlreadyCustom = _customPermissions.any((up) => up.permissionId == entry.key);

          if (isAlreadyCustom) {
            debugPrint('ℹ️ تجاهل الصلاحية ${entry.key} - موجودة بالفعل كصلاحية مخصصة');
            continue;
          }

          final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
          final currentUserId = _authController.currentUser.value?.id ?? 1;

          final userPermission = UserPermission(
            id: 0, // سيتم تعيينه من الخادم
            userId: widget.user.id,
            permissionId: entry.key,
            grantedBy: currentUserId,
            grantedAt: currentTime,
            isActive: true,
            isDeleted: false,
            createdAt: currentTime, // إضافة createdAt
          );

          debugPrint('🔍 محاولة إضافة صلاحية: UserId=${userPermission.userId}, PermissionId=${userPermission.permissionId}');

          // التحقق من وجود الصلاحية أولاً
          try {
            final checkResult = await _userPermissionsApiService.checkUserPermissionById(
              userPermission.userId,
              userPermission.permissionId
            );

            debugPrint('🔍 نتيجة التحقق: $checkResult');

            if (checkResult['exists'] == true && checkResult['canAdd'] == false) {
              debugPrint('ℹ️ الصلاحية ${entry.key} موجودة ونشطة بالفعل - تم تجاهلها');
              continue;
            }

            debugPrint('🔍 إرسال بيانات الصلاحية: ${userPermission.toJson()}');
            final result = await _permissionsApiService.addCustomUserPermission(userPermission);
            debugPrint('✅ تم إضافة الصلاحية بنجاح: ${entry.key} - النتيجة: $result');

          } catch (e) {
            debugPrint('❌ خطأ في إضافة الصلاحية ${entry.key}: $e');

            // التحقق من نوع الخطأ
            final errorMessage = e.toString().toLowerCase();
            if (errorMessage.contains('conflict') ||
                errorMessage.contains('موجودة بالفعل') ||
                errorMessage.contains('unique') ||
                errorMessage.contains('duplicate') ||
                errorMessage.contains('validation errors')) {
              debugPrint('! خطأ في إضافة الصلاحية ${entry.key}: $e');
              // لا نوقف العملية، نكمل مع الصلاحيات الأخرى
            } else {
              debugPrint('! خطأ في إضافة الصلاحية ${entry.key}: $e');
              // لا نوقف العملية، نكمل مع الصلاحيات الأخرى
            }
          }
        }
      }

      // حذف الصلاحيات المخصصة التي تم إلغاؤها
      for (final customPerm in _customPermissions) {
        final shouldKeep = _userPermissions[customPerm.permissionId] ?? false;
        if (!shouldKeep) {
          debugPrint('🗑️ حذف الصلاحية المخصصة: ${customPerm.id}');
          try {
            // استخدام الحذف المنطقي أولاً
            bool success = await _permissionsApiService.softDeleteCustomUserPermission(customPerm.id);

            // إذا فشل الحذف المنطقي، جرب الحذف العادي
            if (!success) {
              debugPrint('⚠️ فشل الحذف المنطقي، محاولة الحذف العادي...');
              success = await _permissionsApiService.deleteCustomUserPermission(customPerm.id);
            }

            if (success) {
              debugPrint('✅ تم حذف الصلاحية المخصصة بنجاح: ${customPerm.id}');
            } else {
              debugPrint('❌ فشل في حذف الصلاحية المخصصة: ${customPerm.id}');
            }
          } catch (e) {
            debugPrint('⚠️ خطأ في حذف الصلاحية المخصصة ${customPerm.id}: $e');
          }
        }
      }

      debugPrint('🎉 تم حفظ جميع تغييرات الصلاحيات بنجاح');

      // إغلاق الحوار أولاً
      Get.back(result: true);

      // ثم إظهار رسالة النجاح بعد تأخير قصير
      await Future.delayed(const Duration(milliseconds: 100));

      AdminMessageDialog.showSuccess(
        title: 'تم الحفظ بنجاح',
        message: 'تم حفظ صلاحيات المستخدم "${widget.user.name}" بنجاح',
      );
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الصلاحيات: $e');

      // تحديد نوع الخطأ لعرض رسالة مناسبة
      String errorMessage = 'فشل في حفظ صلاحيات المستخدم';
      if (e.toString().contains('validation errors')) {
        errorMessage = 'خطأ في التحقق من صحة البيانات. تأكد من صحة المعلومات المدخلة.';
      } else if (e.toString().contains('unique') || e.toString().contains('duplicate')) {
        errorMessage = 'بعض الصلاحيات موجودة بالفعل للمستخدم.';
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        errorMessage = 'خطأ في الاتصال بالخادم. تحقق من اتصال الإنترنت.';
      }

      AdminMessageDialog.showError(
        title: 'خطأ',
        message: errorMessage,
      );
    } finally {
      _isSaving.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppColors.surface,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: AppColors.getBorderColor(),
          width: 1,
        ),
      ),
      child: Obx(() => Container(
        width: _isExpanded.value
            ? MediaQuery.of(context).size.width * 0.98
            : MediaQuery.of(context).size.width * 0.9,
        height: _isExpanded.value
            ? MediaQuery.of(context).size.height * 0.95
            : null,
        constraints: BoxConstraints(
          maxWidth: _isExpanded.value ? double.infinity : 900,
          maxHeight: _isExpanded.value ? double.infinity : 700,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس الحوار المحسن
            _buildDialogHeader(),

            // محتوى الحوار
            Expanded(
              child: Obx(() {
                if (_isLoading.value) {
                  return const Center(child: CircularProgressIndicator());
                }

                return _buildPermissionsList();
              }),
            ),

            // أزرار الحوار
            _buildDialogActions(),
          ],
        ),
      )),
    );
  }

  /// بناء رأس الحوار
  Widget _buildDialogHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Theme.of(context).primaryColor, Theme.of(context).primaryColor.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.security, color: Colors.white, size: 24),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إدارة صلاحيات المستخدم',
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.person, color: AppColors.primary, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          widget.user.name,
                          style: TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),

                       
                      ],
                    ),
                    const SizedBox(height: 4),
                     Row(
                       children: [
                         Icon(Icons.email, color: AppColors.primary, size: 16),
                        const SizedBox(width: 4),
                         Text(
                              widget.user.email ?? 'غير محدد',
                              style: TextStyle(
                                color: AppColors.textSecondary,
                                fontSize: 14,
                              ),
                            ),
                       ],
                     ),
                     const SizedBox(height: 4),
                     //الدور
                     Row(
                       children: [
                         Icon(Icons.badge_outlined, color: AppColors.primary, size: 16),
                        const SizedBox(width: 4),
                         Text(

                           widget.user.role!.displayName,
                            
                              style: TextStyle(
                                color: AppColors.textSecondary,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                       ],
                     ),
                  ],
                ),
              ),
              Obx(() {
                final selectedCount = _userPermissions.values.where((v) => v).length;
                final totalCount = _adminController.permissions.length;
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.textPrimary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '$selectedCount / $totalCount',
                    style: TextStyle(
                      color: AppColors.textPrimary,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              }),
              const SizedBox(width: 8),
              Obx(() => IconButton(
                onPressed: () => _isExpanded.value = !_isExpanded.value,
                icon: Icon(
                  _isExpanded.value ? Icons.fullscreen_exit : Icons.fullscreen,
                  // color: AppColors.primary,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                ),
                tooltip: _isExpanded.value ? 'تصغير الحوار' : 'توسيع الحوار',
              )),
              const SizedBox(width: 8),
              IconButton(
                onPressed: () => Get.back(),
                icon: Icon(Icons.close),
                style: IconButton.styleFrom(
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                ),
                tooltip: 'إغلاق',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الصلاحيات
  Widget _buildPermissionsList() {
    return Column(
      children: [
        // قوالب الصلاحيات السريعة
        _buildQuickTemplates(),

        const SizedBox(height: 16),

        // حقل البحث
        _buildSearchField(),

        const SizedBox(height: 16),

        // شريط التحكم السريع
        _buildQuickControlBar(),

        const SizedBox(height: 16),

        // قائمة الصلاحيات مجمعة
        Expanded(
          child: Obx(() {
            final permissions = _adminController.permissions;
            if (permissions.isEmpty) {
              return const Center(
                child: Text('لا توجد صلاحيات متاحة'),
              );
            }

            return _buildGroupedPermissionsList(permissions);
          }),
        ),
      ],
    );
  }



  /// بناء أزرار الحوار
  Widget _buildDialogActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          const SizedBox(width: 16),
          Obx(() => ElevatedButton(
            onPressed: _isSaving.value || !_permissionService.canManagePermissions()
              ? null
              : _savePermissions,
            child: _isSaving.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('حفظ التغييرات'),
          )),
        ],
      ),
    );
  }

  /// بناء أدوار سريعة
  Widget _buildQuickTemplates() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Icon(Icons.flash_on, color: Colors.orange, size: 18),
              ),
              const SizedBox(width: 12),
              const Text(
                'تطبيق سريع للأدوار:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Obx(() {
            if (_roles.isEmpty) {
              return const Text('لا توجد أدوار متاحة');
            }

            return Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ..._roles.take(4).map((role) => _buildRoleButton(
                  role.displayName,
                  _getRoleIcon(role.name),
                  _getRoleColor(role.name),
                  role,
                )),
                _buildRoleButton('مسح الكل', Icons.clear_all, Colors.grey, null),
              ],
            );
          }),
        ],
      ),
    );
  }

  /// بناء زر دور
  Widget _buildRoleButton(String name, IconData icon, Color color, Role? role) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: () => _applyRolePermissions(role),
        icon: Icon(icon, size: 18),
        label: Text(
          name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  /// الحصول على أيقونة الدور
  IconData _getRoleIcon(String roleName) {
    final name = roleName.toLowerCase();
    if (name.contains('admin') || name.contains('مدير')) {
      return Icons.admin_panel_settings;
    } else if (name.contains('manager') || name.contains('مشرف')) {
      return Icons.manage_accounts;
    } else if (name.contains('supervisor') || name.contains('مراقب')) {
      return Icons.supervisor_account;
    } else if (name.contains('employee') || name.contains('موظف')) {
      return Icons.person;
    } else if (name.contains('viewer') || name.contains('مشاهد')) {
      return Icons.visibility;
    } else {
      return Icons.group;
    }
  }

  /// الحصول على لون الدور
  Color _getRoleColor(String roleName) {
    final name = roleName.toLowerCase();
    if (name.contains('admin') || name.contains('مدير')) {
      return Colors.red;
    } else if (name.contains('manager') || name.contains('مشرف')) {
      return Colors.orange;
    } else if (name.contains('supervisor') || name.contains('مراقب')) {
      return Colors.purple;
    } else if (name.contains('employee') || name.contains('موظف')) {
      return Colors.blue;
    } else if (name.contains('viewer') || name.contains('مشاهد')) {
      return Colors.green;
    } else {
      return Colors.grey;
    }
  }
// AdminConfirmDialog s=AdminConfirmDialog(title: title, message: message)
  /// تطبيق صلاحيات دور
  void _applyRolePermissions(Role? role) async {
    if (role == null) {
      // تأكيد مسح جميع الصلاحيات
      // final confirmed = await AdminConfirmDialog(
      AdminConfirmDialog(
        title: 'مسح جميع الصلاحيات',
        message: 'هل أنت متأكد من مسح جميع صلاحيات المستخدم "${widget.user.name}"؟',
        confirmText: 'مسح الكل',
        icon: Icons.clear_all,
        confirmColor: Colors.orange,
      );

      // if (!confirmed) return;

      // // مسح جميع الصلاحيات
      // _userPermissions.clear();
      // debugPrint('تم مسح جميع الصلاحيات');

      AdminMessageDialog.showSuccess(
        title: 'تم المسح',
        message: 'تم مسح جميع صلاحيات المستخدم "${widget.user.name}"',
      );
      return;
    }

    try {
      // جلب صلاحيات الدور من الباك اند
      final rolePermissions = await _rolesApiService.getRolePermissions(role.id);

      // تطبيق الصلاحيات
      _userPermissions.clear();
      for (final permission in rolePermissions) {
        _userPermissions[permission.id] = true;
      }

      debugPrint('تم تطبيق صلاحيات ${role.displayName}: ${rolePermissions.length} صلاحية');

      AdminMessageDialog.showSuccess(
        title: 'تم التطبيق بنجاح',
        message: 'تم تطبيق صلاحيات ${role.displayName}\nعدد الصلاحيات: ${rolePermissions.length}',
      );
    } catch (e) {
      debugPrint('خطأ في تطبيق صلاحيات الدور: $e');
      AdminMessageDialog.showError(
        title: 'خطأ في التطبيق',
        message: 'فشل في تطبيق صلاحيات الدور:\n$e',
      );
    }
  }

  // تم حذف الدالة القديمة واستبدالها بالنسخة الجديدة التي تستخدم الأدوار من قاعدة البيانات

  // تم حذف دوال التحقق من الصلاحيات القديمة لأنها لم تعد مستخدمة





  /// بناء قائمة الصلاحيات مجمعة
  Widget _buildGroupedPermissionsList(List<Permission> permissions) {
    return Obx(() {
      // تطبيق البحث
      final filteredPermissions = _searchQuery.value.isEmpty
        ? permissions
        : permissions.where((p) =>
            p.name.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
            (p.description?.toLowerCase().contains(_searchQuery.value.toLowerCase()) ?? false)
          ).toList();

      if (filteredPermissions.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.search_off, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('لا توجد صلاحيات تطابق البحث'),
            ],
          ),
        );
      }

      final groupedPermissions = _groupPermissionsByCategory(filteredPermissions);

      return ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        itemCount: groupedPermissions.keys.length,
        itemBuilder: (context, index) {
          final category = groupedPermissions.keys.elementAt(index);
          final categoryPermissions = groupedPermissions[category]!;

          return _buildPermissionGroup(category, categoryPermissions);
        },
      );
    });
  }

  /// تجميع الصلاحيات حسب المجموعة (مثل إدارة الأدوار)
  Map<String, List<Permission>> _groupPermissionsByCategory(List<Permission> permissions) {
    final Map<String, List<Permission>> grouped = {};

    for (final permission in permissions) {
      final group = permission.permissionGroup;
      grouped.putIfAbsent(group, () => []).add(permission);
    }

    return grouped;
  }

  /// بناء مجموعة صلاحيات
  Widget _buildPermissionGroup(String category, List<Permission> permissions) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        title: Text(
          category,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Obx(() {
          final selectedCount = permissions.where((p) => _userPermissions[p.id] ?? false).length;
          return Text('$selectedCount من ${permissions.length} محددة');
        }),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Obx(() {
              final selectedInGroup = permissions.where((p) => _userPermissions[p.id] ?? false).length;
              final isAllSelected = selectedInGroup == permissions.length;
              final isPartiallySelected = selectedInGroup > 0 && selectedInGroup < permissions.length;

              return Container(
                decoration: BoxDecoration(
                  color: isAllSelected
                    ? AppColors.error.withValues(alpha: 0.1)
                    : AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  onPressed: () => _selectAllInGroup(permissions, !isAllSelected),
                  icon: Icon(
                    isAllSelected
                      ? Icons.check_box
                      : isPartiallySelected
                        ? Icons.indeterminate_check_box
                        : Icons.check_box_outline_blank,
                    color: isAllSelected
                      ? AppColors.primary
                      : AppColors.success,
                  ),
                  tooltip: isAllSelected
                    ? 'إلغاء تحديد الكل'
                    : 'تحديد الكل',
                  iconSize: 20,
                ),
              );
            }),
            const SizedBox(width: 8),
            Icon(Icons.expand_more, color: Colors.grey[600]),
          ],
        ),
        children: [
          ...permissions.map((permission) => _buildPermissionTile(permission)),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  /// تحديد/إلغاء تحديد جميع الصلاحيات في مجموعة
  void _selectAllInGroup(List<Permission> permissions, bool select) {
    for (final permission in permissions) {
      _userPermissions[permission.id] = select;
    }
  }

  /// تحديد/إلغاء تحديد جميع الصلاحيات
  void _toggleSelectAll(bool select) {
    final permissions = _adminController.permissions;
    for (final permission in permissions) {
      _userPermissions[permission.id] = select;
    }

    debugPrint('تم ${select ? 'تحديد' : 'إلغاء تحديد'} جميع الصلاحيات (${permissions.length} صلاحية)');
  }

  /// بناء شريط التحكم السريع
  Widget _buildQuickControlBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.getBorderColor()),
        boxShadow: [
          BoxShadow(
            color: AppColors.getShadowColor(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Obx(() {
        final selectedCount = _userPermissions.values.where((v) => v).length;
        final totalCount = _adminController.permissions.length;
        final isAllSelected = selectedCount == totalCount && totalCount > 0;
        final isPartiallySelected = selectedCount > 0 && selectedCount < totalCount;

        return Row(
          children: [
            // أيقونة التحكم
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(Icons.checklist, color: AppColors.primary, size: 18),
            ),
            const SizedBox(width: 12),

            // عداد الصلاحيات المحددة
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: selectedCount > 0
                  ? AppColors.primary.withValues(alpha: 0.1)
                  : AppColors.textSecondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: selectedCount > 0
                    ? AppColors.primary.withValues(alpha: 0.3)
                    : AppColors.textSecondary.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 16,
                    color: selectedCount > 0 ? AppColors.primary : AppColors.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '$selectedCount من $totalCount محددة',
                    style: TextStyle(
                      color: selectedCount > 0 ? AppColors.primary : AppColors.textSecondary,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            const Spacer(),

            // Checkbox تحديد الكل
            Tooltip(
              message: isAllSelected
                ? 'إلغاء تحديد جميع الصلاحيات'
                : 'تحديد جميع الصلاحيات',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'تحديد جميع الصلاحيات',
                    style: TextStyle(
                      color: AppColors.textPrimary,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Checkbox(
                    value: isAllSelected ? true : (isPartiallySelected ? null : false),
                    tristate: true,
                    onChanged: _permissionService.canManagePermissions()
                      ? (value) => _toggleSelectAll(value ?? false)
                      : null,
                    activeColor: AppColors.primary,
                    checkColor: AppColors.white,
                    side: BorderSide(color: AppColors.primary),
                  ),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }

  /// بناء حقل البحث
  Widget _buildSearchField() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في الصلاحيات...',
          prefixIcon: Icon(Icons.search, color: Theme.of(context).primaryColor),
          suffixIcon: Obx(() => _searchQuery.value.isNotEmpty
            ? Container(
                margin: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: IconButton(
                  onPressed: () {
                    _searchController.clear();
                    _searchQuery.value = '';
                  },
                  icon: const Icon(Icons.clear, size: 18),
                  tooltip: 'مسح البحث',
                  color: Colors.grey[600],
                ),
              )
            : const SizedBox.shrink()),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Theme.of(context).primaryColor, width: 2),
          ),
        ),
        onChanged: (value) => _searchQuery.value = value,
      ),
    );
  }

  /// بناء عنصر الصلاحية
  Widget _buildPermissionTile(Permission permission) {
    return Obx(() {
      final hasPermission = _userPermissions[permission.id] ?? false;

      return CheckboxListTile(
        title: Text(
          permission.name,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: permission.description != null
          ? Text(permission.description!)
          : null,
        secondary: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: hasPermission
              ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
              : Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getIconFromString(permission.icon),
            color: hasPermission
              ? Theme.of(context).primaryColor
              : Colors.grey[600],
            size: 20,
          ),
        ),
        value: hasPermission,
        onChanged: _permissionService.canManagePermissions()
          ? (value) {
              setState(() {
                _userPermissions[permission.id] = value ?? false;
              });
            }
          : null,
        activeColor: Theme.of(context).primaryColor,
      );
    });
  }
}
