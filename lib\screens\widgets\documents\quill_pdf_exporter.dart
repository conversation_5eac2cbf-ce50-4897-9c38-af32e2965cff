import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import 'package:path_provider/path_provider.dart';
import 'package:printing/printing.dart';
import 'package:get/get.dart';
import '../../../services/unified_permission_service.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';
import 'quill_error_handler.dart';
import '../unified_pdf_viewer.dart';

/// مصدر PDF محسن لمحرر Quill باستخدام flutter_quill_to_pdf
/// يوفر تحويل كامل للتنسيق مع دعم الألوان والخطوط والأحجام
class QuillPdfExporter {
  // الصلاحيات - غير مستخدمة حالياً
  // final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  final QuillController controller;
  final BuildContext context;
  final String documentTitle;
  final String? documentDescription;

  QuillPdfExporter({
    required this.controller,
    required this.context,
    required this.documentTitle,
    this.documentDescription,
  });

  /// تصدير المستند إلى PDF مع خيارات
  Future<void> exportToPdf() async {
    try {
      final pdf = await _generatePdf();
      if (pdf != null && context.mounted) {
        await _showPdfOptions(pdf);
      }
    } catch (e, stackTrace) {
      if (context.mounted) {
        QuillErrorHandler.handlePdfError(context, e, stackTrace: stackTrace);
      }
    }
  }

  /// إنشاء PDF باستخدام flutter_quill_to_pdf مع الحفاظ على التنسيق
  Future<pw.Document?> _generatePdf() async {
    try {
      debugPrint('📄 بدء إنشاء PDF باستخدام flutter_quill_to_pdf...');

      // تحميل الخطوط العربية
      final arabicFonts = await _loadArabicFonts();

      // سيتم استخدام الطريقة المخصصة بدلاً من PDFConverter

      // إنشاء مستند مخصص مع العنوان والوصف والمحتوى المنسق
      final finalDoc = await _createCustomDocument(arabicFonts);

      debugPrint('✅ تم إنشاء PDF بنجاح مع العنوان والوصف والتنسيق');
      return finalDoc;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء PDF: $e');
      rethrow;
    }
  }

  /// إنشاء وعرض PDF في العارض الموحد مع إمكانية الطباعة
  Future<void> showPdfPreview([pw.Document? pdf]) async {
    try {
      debugPrint('🔄 بدء إنشاء PDF للطباعة...');
      
      // إنشاء PDF إذا لم يتم تمريره
      if (pdf == null) {
        debugPrint('📄 إنشاء PDF جديد...');
        pdf = await _generatePdf();
        debugPrint('✅ تم إنشاء PDF بنجاح');
      }

      debugPrint('💾 حفظ بيانات PDF...');
      final pdfData = await pdf!.save();
      debugPrint('✅ تم حفظ PDF - الحجم: ${pdfData.length} بايت');

      if (context.mounted) {
        debugPrint('🚀 فتح العارض الموحد...');
        Get.to(() => UnifiedPdfViewer(
          pdfData: pdfData,
          fileName: '$documentTitle.pdf',
          title: documentTitle,
          showToolbar: true,
          showPrintButton: true,
          showShareButton: true,
          showSaveButton: true,
        ));
        debugPrint('✅ تم فتح العارض الموحد');
      } else {
        debugPrint('❌ Context غير متاح - لا يمكن فتح العارض');
      }
    } catch (e, stackTrace) {
      debugPrint('❌ خطأ في إنشاء PDF للطباعة: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      
      if (context.mounted) {
        // محاولة الطباعة المباشرة كحل بديل
        try {
          debugPrint('🔄 محاولة الطباعة المباشرة كحل بديل...');
          if (pdf != null) {
            await Printing.layoutPdf(
              onLayout: (format) async => pdf!.save(),
              name: documentTitle,
            );
            debugPrint('✅ تم تشغيل الطباعة المباشرة بنجاح');
          } else {
            throw Exception('لا يمكن إنشاء PDF');
          }
        } catch (printError) {
          debugPrint('❌ فشل في الطباعة المباشرة أيضاً: $printError');
          if (context.mounted) {
            QuillErrorHandler.handlePdfError(context, e, stackTrace: stackTrace);
          }
        }
      }
    }
  }

  /// طباعة PDF مباشرة
  Future<void> _printPdf(pw.Document pdf) async {
    try {
      await Printing.layoutPdf(
        onLayout: (format) async => pdf.save(),
        name: documentTitle,
      );
    } catch (e, stackTrace) {
      if (context.mounted) {
        QuillErrorHandler.handlePdfError(context, e, stackTrace: stackTrace);
      }
    }
  }

  /// عرض خيارات PDF (حفظ، طباعة، معاينة)
  Future<void> _showPdfOptions(pw.Document pdf) async {
    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.dialog,
        title: Text(
          'خيارات PDF',
          style: AppStyles.titleLarge.copyWith(color: AppColors.textPrimary),
        ),
        content: Text(
          'اختر العملية المطلوبة:',
          style: AppStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: AppStyles.textButtonStyle,
            child: Text(
              'إلغاء',
              style: AppStyles.labelMedium.copyWith(color: AppColors.textSecondary),
            ),
          ),
          if (UnifiedPermissionService().canExportDocuments())
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _savePdf(pdf);
              },
              style: AppStyles.secondaryButtonStyle,
              child: Text(
                'حفظ',
                style: AppStyles.labelMedium.copyWith(color: AppColors.white),
              ),
            ),
          if (UnifiedPermissionService().canPrintDocuments())
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _printPdf(pdf);
              },
              style: AppStyles.secondaryButtonStyle,
              child: Text(
                'طباعة مباشرة',
                style: AppStyles.labelMedium.copyWith(color: AppColors.white),
              ),
            ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await showPdfPreview(pdf);
            },
            style: AppStyles.primaryButtonStyle,
            child: Text(
              'معاينة وطباعة',
              style: AppStyles.labelMedium.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }

  /// حفظ PDF في الجهاز
  Future<void> _savePdf(pw.Document pdf) async {
    try {
      final output = await getApplicationDocumentsDirectory();
      final file = File('${output.path}/$documentTitle.pdf');
      await file.writeAsBytes(await pdf.save());

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حفظ PDF في: ${file.path}'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        QuillErrorHandler.handlePdfError(context, e);
      }
    }
  }

  /// تحميل الخطوط العربية المحلية للـ PDF مع دعم fallback
  Future<Map<String, pw.Font>> _loadArabicFonts() async {
    final Map<String, pw.Font> fonts = {};

    try {
      // قائمة الخطوط العربية المتاحة مع أولوية التحميل
      final fontConfigs = [
        {'path': 'assets/fonts/Cairo-Regular.ttf', 'key': 'regular'},
        {'path': 'assets/fonts/Cairo-Bold.ttf', 'key': 'bold'},
        {'path': 'assets/fonts/NotoSansArabic-Regular.ttf', 'key': 'fallback1'},
        {'path': 'assets/fonts/NotoSansArabic-Bold.ttf', 'key': 'fallback1Bold'},
        {'path': 'assets/fonts/Amiri-Regular.ttf', 'key': 'fallback2'},
        {'path': 'assets/fonts/Amiri-Bold.ttf', 'key': 'fallback2Bold'},
      ];

      // محاولة تحميل كل خط متاح
      for (final config in fontConfigs) {
        try {
          final fontData = await rootBundle.load(config['path']!);
          fonts[config['key']!] = pw.Font.ttf(fontData);
          final fontName = config['path']!.split('/').last.split('.').first;
          debugPrint('✅ تم تحميل خط: $fontName');
        } catch (e) {
          final fontName = config['path']!.split('/').last.split('.').first;
          debugPrint('⚠️ لم يتم العثور على خط: $fontName');
        }
      }

      // إضافة خطوط احتياطية إذا لم يتم تحميل أي خط
      if (fonts.isEmpty) {
        debugPrint('⚠️ لم يتم العثور على أي خطوط عربية، استخدام الخطوط الافتراضية');
        fonts['regular'] = pw.Font.helvetica();
        fonts['bold'] = pw.Font.helvetica();
      } else {
        // التأكد من وجود خط عادي وعريض على الأقل
        fonts['regular'] ??= fonts.values.first;
        fonts['bold'] ??= fonts['regular']!;
      }

    } catch (e) {
      debugPrint('❌ خطأ في تحميل الخطوط العربية: $e');
      // استخدام خطوط احتياطية
      fonts['regular'] = pw.Font.helvetica();
      fonts['bold'] = pw.Font.helvetica();
    }

    debugPrint('📝 تم تحميل ${fonts.length} خط للـ PDF');
    return fonts;
  }

  /// إنشاء مستند PDF مخصص مع العنوان والوصف والمحتوى المنسق
  Future<pw.Document> _createCustomDocument(Map<String, pw.Font> arabicFonts) async {
    try {
      final doc = pw.Document();

      // الحصول على محتوى Quill مع التنسيق
      final formattedContent = await _processQuillDelta(arabicFonts);

      doc.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl, // تغيير إلى RTL للعربية
          margin: const pw.EdgeInsets.all(40),
          maxPages: 20, // حد أقصى 20 صفحة
          theme: _createPdfTheme(arabicFonts),
          build: (context) => [
            // العنوان
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColors.blue50,
                border: pw.Border.all(color: PdfColors.blue200),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  pw.Text(
                    documentTitle,
                    style: pw.TextStyle(
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.blue800,
                      font: arabicFonts['bold'],
                      fontFallback: arabicFonts.values.toList(),
                    ),
                  ),
                  if (documentDescription != null && documentDescription!.isNotEmpty) ...[
                    pw.SizedBox(height: 10),
                    pw.Text(
                      documentDescription!,
                      style: pw.TextStyle(
                        fontSize: 14,
                        color: PdfColors.grey700,
                        font: arabicFonts['regular'],
                        fontFallback: arabicFonts.values.toList(),
                      ),
                    ),
                  ],
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // خط فاصل
            pw.Divider(color: PdfColors.grey400, thickness: 1),

            pw.SizedBox(height: 20),

            // المحتوى
            pw.Text(
              'محتوى المستند:',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.grey800,
                font: arabicFonts['bold'],
                fontFallback: arabicFonts.values.toList(),
              ),
            ),

            pw.SizedBox(height: 15),

            // محتوى Quill المنسق
            ...formattedContent,
          ],
        ),
      );

      return doc;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء المستند المخصص: $e');
      rethrow;
    }
  }

  /// معالجة محتوى Quill Delta مع الحفاظ على التنسيق والألوان
  Future<List<pw.Widget>> _processQuillDelta(Map<String, pw.Font> arabicFonts) async {
    try {
      final List<pw.Widget> widgets = [];
      final delta = controller.document.toDelta();

      // تحويل Delta إلى قائمة من العمليات
      final operations = delta.toList();

      if (operations.isEmpty) {
        widgets.add(
          pw.Text(
            'لا يوجد محتوى',
            style: _getDefaultTextStyle(arabicFonts),
          ),
        );
        return widgets;
      }

      // معالجة العمليات كمجموعات نصية منفصلة
      List<pw.RichText> richTextWidgets = [];
      List<pw.InlineSpan> currentSpans = [];

      for (final operation in operations) {
        if (operation.data is String) {
          String text = operation.data as String;

          // تنظيف النص من الرموز المشكلة
          text = _cleanTextForPdf(text);

          if (text.isEmpty) continue;

          // إنشاء النمط لهذا الجزء من النص
          pw.TextStyle textStyle = operation.attributes != null
              ? _createTextStyleFromAttributes(operation.attributes!, arabicFonts)
              : _getDefaultTextStyle(arabicFonts);

          // معالجة الأسطر الجديدة
          if (text.contains('\n')) {
            final lines = text.split('\n');

            // إضافة الجزء الأول للسطر الحالي
            if (lines.first.isNotEmpty) {
              currentSpans.add(
                pw.TextSpan(
                  text: lines.first,
                  style: textStyle,
                ),
              );
            }

            // إنهاء السطر الحالي
            if (currentSpans.isNotEmpty) {
              richTextWidgets.add(
                pw.RichText(
                  text: pw.TextSpan(
                    children: currentSpans,
                    style: _getDefaultTextStyle(arabicFonts),
                  ),
                ),
              );
              currentSpans = [];
            }

            // إضافة الأسطر المتبقية كأسطر منفصلة
            for (int i = 1; i < lines.length; i++) {
              if (i < lines.length - 1) {
                // سطر كامل
                if (lines[i].trim().isNotEmpty) {
                  richTextWidgets.add(
                    pw.RichText(
                      text: pw.TextSpan(
                        text: lines[i],
                        style: textStyle,
                      ),
                    ),
                  );
                } else {
                  // سطر فارغ
                  richTextWidgets.add(
                    pw.RichText(
                      text: pw.TextSpan(
                        text: ' ',
                        style: _getDefaultTextStyle(arabicFonts),
                      ),
                    ),
                  );
                }
              } else {
                // آخر جزء - يبدأ سطر جديد
                if (lines[i].isNotEmpty) {
                  currentSpans.add(
                    pw.TextSpan(
                      text: lines[i],
                      style: textStyle,
                    ),
                  );
                }
              }
            }
          } else {
            // نص عادي بدون أسطر جديدة
            currentSpans.add(
              pw.TextSpan(
                text: text,
                style: textStyle,
              ),
            );
          }
        }
      }

      // إضافة آخر سطر إذا كان موجوداً
      if (currentSpans.isNotEmpty) {
        richTextWidgets.add(
          pw.RichText(
            text: pw.TextSpan(
              children: currentSpans,
              style: _getDefaultTextStyle(arabicFonts),
            ),
          ),
        );
      }

      // تحويل RichText إلى Widgets مع padding
      for (final richText in richTextWidgets) {
        widgets.add(
          pw.Padding(
            padding: const pw.EdgeInsets.only(bottom: 8),
            child: richText,
          ),
        );
      }

      return widgets.isNotEmpty ? widgets : [
        pw.Text(
          'لا يوجد محتوى',
          style: _getDefaultTextStyle(arabicFonts),
        ),
      ];
    } catch (e) {
      debugPrint('❌ خطأ في معالجة Quill Delta: $e');
      return [
        pw.Text(
          'خطأ في معالجة المحتوى',
          style: pw.TextStyle(
            fontSize: 12,
            color: PdfColors.red,
            font: arabicFonts['regular'],
            fontFallback: arabicFonts.values.toList(),
          ),
        ),
      ];
    }
  }

  /// الحصول على النمط الافتراضي للنص
  pw.TextStyle _getDefaultTextStyle(Map<String, pw.Font> arabicFonts) {
    return pw.TextStyle(
      fontSize: 12,
      color: PdfColors.black,
      font: arabicFonts['regular'],
      fontFallback: arabicFonts.values.toList(),
      lineSpacing: 1.4,
    );
  }

  /// إنشاء نمط النص من خصائص Quill
  pw.TextStyle _createTextStyleFromAttributes(
    Map<String, dynamic> attributes,
    Map<String, pw.Font> arabicFonts,
  ) {
    try {
      // البدء بالنمط الافتراضي
      pw.Font baseFont = arabicFonts['regular']!;
      PdfColor textColor = PdfColors.black; // اللون الافتراضي دائماً أسود
      double fontSize = 12.0; // الحجم الافتراضي

      // تطبيق الخصائص فقط إذا كانت موجودة صراحة
      if (attributes.containsKey('bold') && attributes['bold'] == true) {
        baseFont = arabicFonts['bold']!;
      }

      if (attributes.containsKey('color') && attributes['color'] != null) {
        textColor = _parseColor(attributes['color']);
      }

      if (attributes.containsKey('size') && attributes['size'] != null) {
        fontSize = _parseSize(attributes['size']);
      }

      return pw.TextStyle(
        font: baseFont,
        fontFallback: arabicFonts.values.toList(),
        fontSize: fontSize,
        color: textColor,
        fontWeight: attributes.containsKey('bold') && attributes['bold'] == true
            ? pw.FontWeight.bold
            : pw.FontWeight.normal,
        fontStyle: attributes.containsKey('italic') && attributes['italic'] == true
            ? pw.FontStyle.italic
            : pw.FontStyle.normal,
        decoration: attributes.containsKey('underline') && attributes['underline'] == true
            ? pw.TextDecoration.underline
            : null,
        lineSpacing: 1.4,
      );
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء نمط النص: $e');
      return _getDefaultTextStyle(arabicFonts);
    }
  }

  /// تحويل لون من تنسيق Quill إلى PdfColor
  PdfColor _parseColor(dynamic colorValue) {
    try {
      if (colorValue is String) {
        // إزالة # إذا كانت موجودة
        String colorString = colorValue.replaceAll('#', '');

        // تحويل إلى int
        int colorInt = int.parse(colorString, radix: 16);

        // استخراج مكونات RGB
        int r = (colorInt >> 16) & 0xFF;
        int g = (colorInt >> 8) & 0xFF;
        int b = colorInt & 0xFF;

        return PdfColor.fromInt(0xFF000000 | (r << 16) | (g << 8) | b);
      }
      return PdfColors.black;
    } catch (e) {
      debugPrint('❌ خطأ في تحويل اللون: $e');
      return PdfColors.black;
    }
  }

  /// تحويل حجم الخط من تنسيق Quill
  double _parseSize(dynamic sizeValue) {
    try {
      if (sizeValue is String) {
        // إزالة 'px' إذا كانت موجودة
        String sizeString = sizeValue.replaceAll('px', '');
        return double.parse(sizeString);
      } else if (sizeValue is num) {
        return sizeValue.toDouble();
      }
      return 12.0;
    } catch (e) {
      debugPrint('❌ خطأ في تحويل حجم الخط: $e');
      return 12.0;
    }
  }

  /// تنظيف النص من الرموز المشكلة
  String _cleanTextForPdf(String text) {
    if (text.isEmpty) return text;

    try {
      return text
          // إزالة الإيموجي والرموز التي لا تدعمها الخطوط العربية
          .replaceAll(RegExp(r'[\u{1F300}-\u{1F9FF}]', unicode: true), '') // إيموجي
          .replaceAll(RegExp(r'[\u{2600}-\u{26FF}]', unicode: true), '') // رموز متنوعة
          .replaceAll(RegExp(r'[\u{2700}-\u{27BF}]', unicode: true), '') // رموز إضافية
          // إزالة أحرف الجداول والرسم
          .replaceAll(RegExp(r'[\u{2500}-\u{257F}]', unicode: true), '') // أحرف الجداول
          .replaceAll(RegExp(r'[\u{2580}-\u{259F}]', unicode: true), '') // أحرف الرسم
          // إزالة Variation Selectors المشكلة (مثل U+fe0f)
          .replaceAll(RegExp(r'[\uFE00-\uFE0F]'), '') // Variation Selectors
          .replaceAll(RegExp(r'[\uE000-\uF8FF]'), '') // Private Use Area
          // إزالة أحرف التحكم الخاصة (ما عدا \n و \t)
          .replaceAll(RegExp(r'[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F-\u009F]'), '')
          // إزالة الأحرف الخاصة التي تسبب مشاكل في PDF
          .replaceAll(RegExp(r'[\uFEFF\u200B-\u200D\u2060\u061C]'), '')
          // إزالة أحرف Unicode الخاصة
          .replaceAll(RegExp(r'[\u202A-\u202E\u2066-\u2069]'), '')
          // إزالة Zero Width Characters
          .replaceAll(RegExp(r'[\u200C\u200D]'), '') // Zero Width Non-Joiner/Joiner
          // تحويل الأحرف الخاصة إلى مسافات
          .replaceAll(RegExp(r'[\u00A0\u1680\u2000-\u200A\u202F\u205F\u3000]'), ' ')
          // إزالة المسافات الزائدة
          .replaceAll(RegExp(r'[ \t]+'), ' ');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف النص: $e');
      return text;
    }
  }

  /// إنشاء Theme للـ PDF مع دعم الخطوط العربية
  pw.ThemeData _createPdfTheme(Map<String, pw.Font> arabicFonts) {
    try {
      if (arabicFonts.isNotEmpty) {
        return pw.ThemeData.withFont(
          base: arabicFonts['regular']!,
          bold: arabicFonts['bold']!,
          italic: arabicFonts['regular']!, // استخدام نفس الخط للمائل
          boldItalic: arabicFonts['bold']!, // استخدام الخط العريض للمائل العريض
          fontFallback: arabicFonts.values.toList(),
        );
      } else {
        debugPrint('⚠️ لا توجد خطوط عربية، استخدام Theme الافتراضي');
        return pw.ThemeData.base();
      }
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء PDF Theme: $e');
      return pw.ThemeData.base();
    }
  }
}
