import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../models/system_models.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_dialog_widget.dart';

/// شاشة إدارة النسخ الاحتياطية
class BackupManagementScreen extends StatefulWidget {
  const BackupManagementScreen({super.key});

  @override
  State<BackupManagementScreen> createState() => _BackupManagementScreenState();
}

class _BackupManagementScreenState extends State<BackupManagementScreen> {
  final AdminController _adminController = Get.find<AdminController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  void initState() {
    super.initState();
    _loadBackups();
  }

  /// تحميل النسخ الاحتياطية
  Future<void> _loadBackups() async {
    await _adminController.loadBackups();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة النسخ الاحتياطية'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBackups,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadBackups,
        child: Column(
          children: [
            // قسم الإحصائيات
            _buildStatsSection(),
            
            // قسم الإجراءات السريعة
            _buildQuickActionsSection(),
            
            const Divider(),
            
            // قائمة النسخ الاحتياطية
            Expanded(
              child: _buildBackupsList(),
            ),
          ],
        ),
      ),
      floatingActionButton: _permissionService.canAccessAdmin()
          ? FloatingActionButton.extended(
              onPressed: _createBackup,
              icon: const Icon(Icons.backup),
              label: const Text('إنشاء نسخة احتياطية'),
              backgroundColor: Colors.green,
            )
          : null,
    );
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Obx(() {
        final backups = _adminController.backups;
        final totalSize = backups.fold<double>(0, (sum, backup) => sum + (backup.size ?? 0));
        
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي النسخ',
                '${backups.length}',
                Icons.backup,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'الحجم الإجمالي',
                _formatFileSize(totalSize),
                Icons.storage,
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'آخر نسخة',
                backups.isNotEmpty 
                  ? _formatDate(backups.first.createdAt)
                  : 'لا توجد',
                Icons.schedule,
                Colors.orange,
              ),
            ),
          ],
        );
      }),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قسم الإجراءات السريعة
  Widget _buildQuickActionsSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _permissionService.canAccessAdmin() ? _createBackup : null,
              icon: const Icon(Icons.backup),
              label: const Text('إنشاء نسخة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _permissionService.canAccessAdmin() ? _showRestoreDialog : null,
              icon: const Icon(Icons.restore),
              label: const Text('استعادة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _showScheduleDialog,
              icon: const Icon(Icons.schedule),
              label: const Text('جدولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _permissionService.canAccessAdmin() ? _showCleanupDialog : null,
              icon: const Icon(Icons.cleaning_services),
              label: const Text('تنظيف'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة النسخ الاحتياطية
  Widget _buildBackupsList() {
    return Obx(() {
      if (_adminController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      final backups = _adminController.backups;
      
      if (backups.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.backup_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد نسخ احتياطية',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'انقر على زر "إنشاء نسخة احتياطية" لإنشاء أول نسخة',
                style: TextStyle(
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: backups.length,
        itemBuilder: (context, index) {
          final backup = backups[index];
          return _buildBackupCard(backup);
        },
      );
    });
  }

  /// بناء بطاقة النسخة الاحتياطية
  Widget _buildBackupCard(Backup backup) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getBackupStatusColor(backup.status),
          child: Icon(
            _getBackupStatusIcon(backup.status),
            color: Colors.white,
          ),
        ),
        title: Text(backup.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الحجم: ${_formatFileSize(backup.size ?? 0)}'),
            Text('التاريخ: ${_formatDate(backup.createdAt)}'),
            if (backup.description?.isNotEmpty == true)
              Text('الوصف: ${backup.description}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (action) => _handleBackupAction(action, backup),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'restore',
              child: ListTile(
                leading: Icon(Icons.restore),
                title: Text('استعادة'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'download',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('تحميل'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        onTap: () => _showBackupDetails(backup),
      ),
    );
  }

  /// إنشاء نسخة احتياطية جديدة
  Future<void> _createBackup() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إنشاء نسخة احتياطية',
      message: 'هل تريد إنشاء نسخة احتياطية جديدة من البيانات؟',
      confirmText: 'إنشاء',
      cancelText: 'إلغاء',
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إنشاء النسخة الاحتياطية...');
      
      try {
        // إنشاء نسخة احتياطية حقيقية
        await _adminController.createBackup('نسخة احتياطية يدوية');
        await _adminController.loadBackups();

        AdminLoadingDialog.hide();
        
        Get.snackbar(
          'نجح',
          'تم إنشاء النسخة الاحتياطية بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في إنشاء النسخة الاحتياطية: $e',
        );
      }
    }
  }

  /// عرض حوار الاستعادة
  void _showRestoreDialog() {
    final backups = _adminController.backups;
    if (backups.isEmpty) {
      Get.snackbar(
        'تنبيه',
        'لا توجد نسخ احتياطية متاحة للاستعادة',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    Get.dialog(
      AlertDialog(
        title: const Text('استعادة نسخة احتياطية'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: Column(
            children: [
              const Text('اختر النسخة الاحتياطية التي تريد استعادتها:'),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: backups.length,
                  itemBuilder: (context, index) {
                    final backup = backups[index];
                    return ListTile(
                      leading: Icon(
                        _getBackupStatusIcon(backup.status),
                        color: _getBackupStatusColor(backup.status),
                      ),
                      title: Text(backup.name),
                      subtitle: Text('${_formatFileSize(backup.size ?? 0)} - ${_formatDate(backup.createdAt)}'),
                      onTap: () {
                        Get.back();
                        _confirmRestore(backup);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// تأكيد الاستعادة
  void _confirmRestore(Backup backup) async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'تأكيد الاستعادة',
      message: 'تحذير: ستؤدي هذه العملية إلى استبدال البيانات الحالية بالبيانات من النسخة الاحتياطية "${backup.name}". هل تريد المتابعة؟',
      confirmText: 'استعادة',
      cancelText: 'إلغاء',
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري استعادة النسخة الاحتياطية...');

      try {
        // استعادة النسخة الاحتياطية الحقيقية
        await _adminController.restoreBackup(backup.id);

        AdminLoadingDialog.hide();

        AdminMessageDialog.showSuccess(
          title: 'نجحت العملية',
          message: 'تم استعادة النسخة الاحتياطية "${backup.name}" بنجاح',
        );

        // إعادة تحميل البيانات
        await _adminController.refreshAllData();
      } catch (e) {
        AdminLoadingDialog.hide();

        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في استعادة النسخة الاحتياطية: $e',
        );
      }
    }
  }

  /// عرض حوار الجدولة
  void _showScheduleDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('جدولة النسخ الاحتياطية'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('اختر تكرار النسخ الاحتياطية التلقائية:'),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.schedule),
                title: const Text('يومياً'),
                subtitle: const Text('إنشاء نسخة احتياطية كل يوم في الساعة 2:00 صباحاً'),
                onTap: () => _setSchedule('daily'),
              ),
              ListTile(
                leading: const Icon(Icons.schedule),
                title: const Text('أسبوعياً'),
                subtitle: const Text('إنشاء نسخة احتياطية كل أسبوع يوم الأحد'),
                onTap: () => _setSchedule('weekly'),
              ),
              ListTile(
                leading: const Icon(Icons.schedule),
                title: const Text('شهرياً'),
                subtitle: const Text('إنشاء نسخة احتياطية في أول يوم من كل شهر'),
                onTap: () => _setSchedule('monthly'),
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text('إيقاف الجدولة'),
                subtitle: const Text('إيقاف النسخ الاحتياطية التلقائية'),
                onTap: () => _setSchedule('none'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// تعيين جدولة النسخ الاحتياطية
  void _setSchedule(String schedule) {
    Get.back();

    String message;
    switch (schedule) {
      case 'daily':
        message = 'تم تفعيل النسخ الاحتياطية اليومية';
        break;
      case 'weekly':
        message = 'تم تفعيل النسخ الاحتياطية الأسبوعية';
        break;
      case 'monthly':
        message = 'تم تفعيل النسخ الاحتياطية الشهرية';
        break;
      case 'none':
        message = 'تم إيقاف النسخ الاحتياطية التلقائية';
        break;
      default:
        message = 'تم تحديث إعدادات الجدولة';
    }

    Get.snackbar(
      'تم التحديث',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  /// معالجة إجراءات النسخة الاحتياطية
  void _handleBackupAction(String action, Backup backup) {
    switch (action) {
      case 'restore':
        _restoreBackup(backup);
        break;
      case 'download':
        _downloadBackup(backup);
        break;
      case 'delete':
        _deleteBackup(backup);
        break;
    }
  }

  /// استعادة نسخة احتياطية
  void _restoreBackup(Backup backup) {
    _confirmRestore(backup);
  }

  /// تحميل نسخة احتياطية
  void _downloadBackup(Backup backup) async {
    try {
      AdminLoadingDialog.show(message: 'جاري تحميل النسخة الاحتياطية...');

      // تحميل النسخة الاحتياطية
      final downloadUrl = await _adminController.downloadBackup(backup.id);

      AdminLoadingDialog.hide();

      // فتح رابط التحميل في المتصفح
      if (downloadUrl.isNotEmpty) {
        // فتح الرابط في نافذة جديدة
        Get.snackbar(
          'نجح',
          'تم بدء تحميل النسخة الاحتياطية "${backup.name}"',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          mainButton: TextButton(
            onPressed: () {
              // يمكن نسخ الرابط أو فتحه
              Get.dialog(
                AlertDialog(
                  title: const Text('رابط التحميل'),
                  content: SelectableText(downloadUrl),
                  actions: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              );
            },
            child: const Text('عرض الرابط', style: TextStyle(color: Colors.white)),
          ),
        );
      }
    } catch (e) {
      AdminLoadingDialog.hide();

      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل النسخة الاحتياطية: $e',
      );
    }
  }

  /// حذف نسخة احتياطية
  void _deleteBackup(Backup backup) async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'تأكيد الحذف',
      message: 'هل تريد حذف النسخة الاحتياطية "${backup.name}"؟\n\nتحذير: لا يمكن التراجع عن هذه العملية.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري حذف النسخة الاحتياطية...');

      try {
        await _adminController.deleteBackup(backup.id);
        await _adminController.loadBackups();

        AdminLoadingDialog.hide();

        Get.snackbar(
          'نجح',
          'تم حذف النسخة الاحتياطية "${backup.name}" بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        AdminLoadingDialog.hide();

        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في حذف النسخة الاحتياطية: $e',
        );
      }
    }
  }

  /// عرض تفاصيل النسخة الاحتياطية
  void _showBackupDetails(Backup backup) {
    Get.dialog(
      AlertDialog(
        title: Text('تفاصيل النسخة الاحتياطية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('الاسم', backup.name),
            _buildDetailRow('الحجم', _formatFileSize(backup.size ?? 0)),
            _buildDetailRow('التاريخ', _formatDate(backup.createdAt)),
            _buildDetailRow('الحالة', backup.status ?? 'غير محدد'),
            if (backup.description?.isNotEmpty == true)
              _buildDetailRow('الوصف', backup.description!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء صف تفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// الحصول على لون حالة النسخة الاحتياطية
  Color _getBackupStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return Colors.green;
      case 'failed':
      case 'error':
        return Colors.red;
      case 'in_progress':
      case 'running':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة حالة النسخة الاحتياطية
  IconData _getBackupStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return Icons.check;
      case 'failed':
      case 'error':
        return Icons.error;
      case 'in_progress':
      case 'running':
        return Icons.hourglass_empty;
      default:
        return Icons.backup;
    }
  }

  /// تنسيق حجم الملف
  String _formatFileSize(double bytes) {
    if (bytes < 1024) return '${bytes.toInt()} B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// تنسيق التاريخ
  String _formatDate(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }



  /// عرض حوار تنظيف النسخ القديمة
  void _showCleanupDialog() {
    int selectedDays = 30;

    Get.dialog(
      AlertDialog(
        title: const Text('تنظيف النسخ الاحتياطية القديمة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('سيتم حذف النسخ الاحتياطية الأقدم من:'),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              value: selectedDays,
              decoration: const InputDecoration(
                labelText: 'عدد الأيام',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 7, child: Text('7 أيام')),
                DropdownMenuItem(value: 14, child: Text('14 يوم')),
                DropdownMenuItem(value: 30, child: Text('30 يوم')),
                DropdownMenuItem(value: 60, child: Text('60 يوم')),
                DropdownMenuItem(value: 90, child: Text('90 يوم')),
              ],
              onChanged: (value) {
                selectedDays = value ?? 30;
              },
            ),
            const SizedBox(height: 16),
            const Text(
              'تحذير: لا يمكن التراجع عن هذه العملية',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _cleanupOldBackups(selectedDays);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('تنظيف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// تنظيف النسخ الاحتياطية القديمة
  void _cleanupOldBackups(int daysOld) async {
    AdminLoadingDialog.show(message: 'جاري تنظيف النسخ القديمة...');

    try {
      final deletedCount = await _adminController.cleanupOldBackups(daysOld);
      await _adminController.loadBackups();

      AdminLoadingDialog.hide();

      Get.snackbar(
        'نجح',
        'تم حذف $deletedCount نسخة احتياطية قديمة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      AdminLoadingDialog.hide();

      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تنظيف النسخ القديمة: $e',
      );
    }
  }
}
