import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io';
import '../services/api/api_service.dart';

/// تعداد أنواع الأخطاء
enum ErrorType {
  network,
  timeout,
  unauthorized,
  forbidden,
  notFound,
  serverError,
  validation,
  unknown
}

/// فئة الأخطاء المخصصة المحسنة
class AppException implements Exception {
  final String message;
  final int? statusCode;
  final ErrorType type;
  final dynamic originalError;
  final Map<String, dynamic>? details;
  final DateTime timestamp;

  AppException({
    required this.message,
    this.statusCode,
    required this.type,
    this.originalError,
    this.details,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'AppException: $message (Status: $statusCode, Type: $type, Time: $timestamp)';
  }

  /// تحويل من ApiException
  factory AppException.fromApiException(ApiException apiException) {
    ErrorType type;
    
    if (apiException.isNetworkError) {
      type = ErrorType.network;
    } else if (apiException.statusCode == 401) {
      type = ErrorType.unauthorized;
    } else if (apiException.statusCode == 403) {
      type = ErrorType.forbidden;
    } else if (apiException.statusCode == 404) {
      type = ErrorType.notFound;
    } else if (apiException.statusCode >= 500) {
      type = ErrorType.serverError;
    } else if (apiException.statusCode == 400) {
      type = ErrorType.validation;
    } else {
      type = ErrorType.unknown;
    }

    return AppException(
      message: apiException.message,
      statusCode: apiException.statusCode,
      type: type,
      originalError: apiException,
    );
  }

  /// تحويل من SocketException
  factory AppException.fromSocketException(SocketException socketException) {
    return AppException(
      message: 'فشل في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت.',
      type: ErrorType.network,
      originalError: socketException,
    );
  }

  /// تحويل من TimeoutException
  factory AppException.fromTimeoutException(dynamic timeoutException) {
    return AppException(
      message: 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.',
      type: ErrorType.timeout,
      originalError: timeoutException,
    );
  }
}

/// معالج الأخطاء الرئيسي
class ErrorHandler {
  static final List<AppException> _errorHistory = [];
  static const int _maxHistorySize = 50;

  /// معالجة الخطأ وعرضه للمستخدم
  static void handleError(dynamic error, {
    bool showSnackbar = true,
    String? customMessage,
    VoidCallback? onRetry,
  }) {
    final appException = _convertToAppException(error, customMessage);

    // إضافة الخطأ للتاريخ
    _addToHistory(appException);

    // طباعة الخطأ للتشخيص
    debugPrint('🔴 Error: ${appException.toString()}');

    // عرض الخطأ للمستخدم
    if (showSnackbar) {
      _showErrorSnackbar(appException, customMessage, onRetry);
    }

    // معالجة خاصة لأخطاء المصادقة
    if (appException.type == ErrorType.unauthorized) {
      _handleUnauthorizedError();
    }
  }

  /// تحويل أي خطأ إلى AppException
  static AppException _convertToAppException(dynamic error, [String? customMessage]) {
    if (error is AppException) {
      return error;
    } else if (error is ApiException) {
      return AppException.fromApiException(error);
    } else if (error is SocketException) {
      return AppException.fromSocketException(error);
    } else if (error.toString().contains('timeout') || error.toString().contains('TimeoutException')) {
      return AppException.fromTimeoutException(error);
    } else {
      return AppException(
        message: customMessage ?? 'حدث خطأ غير متوقع: ${error.toString()}',
        type: ErrorType.unknown,
        originalError: error,
      );
    }
  }

  /// إضافة الخطأ لتاريخ الأخطاء
  static void _addToHistory(AppException error) {
    _errorHistory.add(error);
    
    // الحفاظ على حجم التاريخ
    if (_errorHistory.length > _maxHistorySize) {
      _errorHistory.removeAt(0);
    }
  }

  /// عرض رسالة الخطأ
  static void _showErrorSnackbar(AppException error, String? customMessage, VoidCallback? onRetry) {
    final message = customMessage ?? error.message;
    final title = _getErrorTitle(error.type);
    final color = _getErrorColor(error.type);
    final duration = _getErrorDuration(error.type);

    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: color,
      colorText: Colors.white,
      duration: Duration(seconds: duration),
      isDismissible: true,
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      icon: Icon(_getErrorIcon(error.type), color: Colors.white),
      mainButton: onRetry != null
          ? TextButton(
              onPressed: onRetry,
              child: const Text('إعادة المحاولة', style: TextStyle(color: Colors.white)),
            )
          : null,
    );
  }

  /// الحصول على عنوان الخطأ
  static String _getErrorTitle(ErrorType type) {
    switch (type) {
      case ErrorType.network:
        return 'خطأ في الشبكة';
      case ErrorType.timeout:
        return 'انتهت مهلة الاتصال';
      case ErrorType.unauthorized:
        return 'خطأ في المصادقة';
      case ErrorType.forbidden:
        return 'ممنوع الوصول';
      case ErrorType.notFound:
        return 'غير موجود';
      case ErrorType.serverError:
        return 'خطأ في الخادم';
      case ErrorType.validation:
        return 'خطأ في البيانات';
      case ErrorType.unknown:
        return 'خطأ غير معروف';
    }
  }

  /// الحصول على لون الخطأ
  static Color _getErrorColor(ErrorType type) {
    switch (type) {
      case ErrorType.network:
      case ErrorType.timeout:
        return Colors.orange.shade700;
      case ErrorType.unauthorized:
      case ErrorType.forbidden:
        return Colors.red.shade700;
      case ErrorType.validation:
        return Colors.amber.shade700;
      case ErrorType.serverError:
        return Colors.red.shade800;
      default:
        return Colors.grey.shade700;
    }
  }

  /// الحصول على مدة عرض الخطأ
  static int _getErrorDuration(ErrorType type) {
    switch (type) {
      case ErrorType.network:
      case ErrorType.timeout:
        return 6;
      case ErrorType.unauthorized:
        return 8;
      case ErrorType.validation:
        return 4;
      default:
        return 5;
    }
  }

  /// الحصول على أيقونة الخطأ
  static IconData _getErrorIcon(ErrorType type) {
    switch (type) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.timeout:
        return Icons.access_time;
      case ErrorType.unauthorized:
      case ErrorType.forbidden:
        return Icons.lock;
      case ErrorType.notFound:
        return Icons.search_off;
      case ErrorType.serverError:
        return Icons.error;
      case ErrorType.validation:
        return Icons.warning;
      default:
        return Icons.error_outline;
    }
  }

  /// معالجة خطأ عدم المصادقة
  static void _handleUnauthorizedError() {
    // تأخير قصير قبل إعادة التوجيه
    Future.delayed(const Duration(seconds: 2), () {
      Get.offAllNamed('/login');
    });
  }

  /// الحصول على تاريخ الأخطاء
  static List<AppException> get errorHistory => List.unmodifiable(_errorHistory);

  /// مسح تاريخ الأخطاء
  static void clearHistory() {
    _errorHistory.clear();
  }

  /// الحصول على إحصائيات الأخطاء
  static Map<ErrorType, int> getErrorStats() {
    final stats = <ErrorType, int>{};
    for (final error in _errorHistory) {
      stats[error.type] = (stats[error.type] ?? 0) + 1;
    }
    return stats;
  }
}

/// مساعد لمعالجة الأخطاء في العمليات غير المتزامنة
class AsyncErrorHandler {
  /// تنفيذ عملية مع معالجة الأخطاء
  static Future<T?> execute<T>(
    Future<T> Function() operation, {
    String? errorMessage,
    bool showError = true,
    VoidCallback? onError,
  }) async {
    try {
      return await operation();
    } catch (error) {
      if (showError) {
        ErrorHandler.handleError(error, customMessage: errorMessage);
      }
      onError?.call();
      return null;
    }
  }

  /// تنفيذ عملية مع إعادة المحاولة الذكية - محسن
  static Future<T?> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
    String? errorMessage,
    bool showError = true,
    bool Function(dynamic error)? shouldRetry,
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        debugPrint('❌ محاولة $attempt من $maxRetries فشلت: $error');

        // التحقق من إمكانية إعادة المحاولة
        if (shouldRetry != null && !shouldRetry(error)) {
          debugPrint('🚫 لا يمكن إعادة المحاولة لهذا النوع من الأخطاء');
          if (showError) {
            ErrorHandler.handleError(error, customMessage: errorMessage);
          }
          return null;
        }

        if (attempt == maxRetries) {
          debugPrint('🔴 فشلت جميع المحاولات ($maxRetries)');
          if (showError) {
            ErrorHandler.handleError(error, customMessage: errorMessage);
          }
          return null;
        }

        // تأخير متزايد قبل إعادة المحاولة (exponential backoff)
        final retryDelay = Duration(milliseconds: delay.inMilliseconds * (attempt * attempt));
        debugPrint('⏳ انتظار ${retryDelay.inSeconds} ثانية قبل المحاولة التالية...');
        await Future.delayed(retryDelay);
      }
    }
    return null;
  }

  /// تنفيذ عملية مع إعادة المحاولة للشبكة فقط
  static Future<T?> executeWithNetworkRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 2),
    String? errorMessage,
    bool showError = true,
  }) async {
    return executeWithRetry<T>(
      operation,
      maxRetries: maxRetries,
      delay: delay,
      errorMessage: errorMessage,
      showError: showError,
      shouldRetry: (error) {
        // إعادة المحاولة فقط لأخطاء الشبكة
        final errorString = error.toString().toLowerCase();
        return errorString.contains('socket') ||
               errorString.contains('network') ||
               errorString.contains('timeout') ||
               errorString.contains('connection') ||
               (error is ApiException && error.statusCode >= 500);
      },
    );
  }
}
