import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/message_models.dart';
import '../../../models/message_attachment_models.dart';
import '../../../models/user_model.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/date_time_formatter.dart';
import '../../../config/api_config.dart';
import '../../../controllers/user_controller.dart';
import '../../../controllers/unified_chat_controller.dart';
import '../../../services/api/message_attachments_api_service.dart';
import 'message_reactions_widget.dart';

/// ويدجت لعرض عنصر رسالة في المحادثة
class MessageItemWidget extends StatefulWidget {
  /// الرسالة
  final Message message;

  /// ما إذا كان المستخدم الحالي هو مرسل الرسالة
  final bool isCurrentUser;

  /// ما إذا كان يجب عرض الصورة الرمزية
  final bool showAvatar;

  /// معرف المستخدم الحالي
  final String currentUserId;

  /// ما إذا كان المستخدم الحالي مشرفًا
  final bool isAdmin;

  /// دالة يتم استدعاؤها عند الرد على الرسالة
  final Function(Message)? onReply;

  /// دالة يتم استدعاؤها عند تثبيت الرسالة
  final Function(Message)? onPin;

  /// دالة يتم استدعاؤها عند إلغاء تثبيت الرسالة
  final Function(Message)? onUnpin;

  /// دالة يتم استدعاؤها عند تعليم الرسالة للمتابعة
  final Function(Message)? onMarkForFollowUp;

  /// دالة يتم استدعاؤها عند تحرير الرسالة
  final Function(Message)? onEdit;

  /// دالة يتم استدعاؤها عند حذف الرسالة
  final Function(Message)? onDelete;

  const MessageItemWidget({
    super.key,
    required this.message,
    required this.isCurrentUser,
    required this.showAvatar,
    required this.currentUserId,
    this.isAdmin = false,
    this.onReply,
    this.onPin,
    this.onUnpin,
    this.onMarkForFollowUp,
    this.onEdit,
    this.onDelete,
  });

  @override
  State<MessageItemWidget> createState() => _MessageItemWidgetState();
}

class _MessageItemWidgetState extends State<MessageItemWidget> {
  final UserController _userController = Get.find<UserController>();

  User? _sender;
  User? _replyToUser;
  Message? _replyToMessage;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void didUpdateWidget(MessageItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.message.id != widget.message.id) {
      _loadData();
    }
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل معلومات المرسل
      _sender = _userController.getUserById(widget.message.senderId);

      // تحميل معلومات الرسالة المرد عليها إذا وجدت
      if (widget.message.replyToMessageId != null) {
        try {
          // محاولة الحصول على الرسالة المرد عليها من المتحكم الموحد
          final chatController = Get.find<UnifiedChatController>();
          // البحث في قائمة الرسائل المحملة
          _replyToMessage = chatController.messages.firstWhereOrNull(
            (msg) => msg.id == widget.message.replyToMessageId,
          );

          if (_replyToMessage != null) {
            _replyToUser = _userController.getUserById(_replyToMessage!.senderId);
          }
        } catch (e) {
          debugPrint('خطأ في تحميل الرسالة المرد عليها: $e');
          // في حالة عدم وجود المتحكم، نحاول إنشاء رسالة مؤقتة
          _replyToMessage = null;
          _replyToUser = null;
        }
      }
    } catch (e) {
      // التعامل مع الخطأ بصمت
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// بناء محتوى الرسالة حسب نوعها
  Widget _buildMessageContent() {
    debugPrint('🎨 بناء محتوى الرسالة ${widget.message.id} - نوع المحتوى: ${widget.message.contentType}');

    switch (widget.message.contentType) {
      case MessageContentType.file:
        debugPrint('📁 عرض رسالة ملف للرسالة ${widget.message.id}');
        return _buildFileMessage();
      case MessageContentType.image:
        return _buildImageMessage();
      case MessageContentType.voice:
        return _buildVoiceMessage();
      case MessageContentType.video:
        return _buildVideoMessage();
      case MessageContentType.location:
        return _buildLocationMessage();
      case MessageContentType.text:
      default:
        debugPrint('📝 عرض رسالة نصية للرسالة ${widget.message.id}');
        return _buildTextMessage();
    }
  }

  /// بناء رسالة نصية
  Widget _buildTextMessage() {
    return Text(
      widget.message.content,
      style: TextStyle(
        color: widget.isCurrentUser ? Colors.white : Colors.black,
      ),
    );
  }

  /// بناء رسالة ملف
  Widget _buildFileMessage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // النص المرافق للملف (إذا كان موجود وليس مجرد وصف للمرفق)
        if (widget.message.content.isNotEmpty &&
            !_isDefaultAttachmentText(widget.message.content))
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              widget.message.content,
              style: TextStyle(
                color: widget.isCurrentUser ? Colors.white : Colors.black,
              ),
            ),
          ),
        // عرض المرفقات
        _buildAttachments(),
      ],
    );
  }

  /// التحقق من أن النص هو وصف افتراضي للمرفق
  bool _isDefaultAttachmentText(String content) {
    final defaultTexts = [
      'ملف مرفق',
      'صورة من المعرض',
      'صورة من الكاميرا',
      'ملف من المستندات',
      'تم إرسال مرفق',
    ];
    return defaultTexts.any((text) => content.contains(text));
  }

  /// بناء عرض المرفقات
  Widget _buildAttachments() {
    debugPrint('🔨 بناء عرض المرفقات للرسالة ${widget.message.id}');
    return FutureBuilder<List<MessageAttachment>>(
      future: _loadMessageAttachments(),
      builder: (context, snapshot) {
        debugPrint('📊 حالة FutureBuilder: ${snapshot.connectionState}');
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: widget.isCurrentUser ? Colors.white.withValues(alpha: 0.2) : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      widget.isCurrentUser ? Colors.white : Colors.grey,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'جاري تحميل المرفقات...',
                  style: TextStyle(
                    color: widget.isCurrentUser ? Colors.white70 : Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          );
        }

        if (snapshot.hasError) {
          return Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.error, size: 16, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'خطأ في تحميل المرفقات',
                  style: TextStyle(color: Colors.red, fontSize: 12),
                ),
              ],
            ),
          );
        }

        final attachments = snapshot.data ?? [];
        debugPrint('📎 عدد المرفقات المحملة: ${attachments.length}');
        if (attachments.isEmpty) {
          debugPrint('⚠️ لا توجد مرفقات للرسالة ${widget.message.id}');
          return Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: widget.isCurrentUser ? Colors.white.withValues(alpha: 0.2) : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.attach_file,
                  size: 16,
                  color: widget.isCurrentUser ? Colors.white70 : Colors.grey[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'لا توجد مرفقات',
                  style: TextStyle(
                    color: widget.isCurrentUser ? Colors.white70 : Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          );
        }

        debugPrint('✅ عرض ${attachments.length} مرفق للرسالة ${widget.message.id}');
        return Column(
          children: attachments.map((attachment) {
            debugPrint('🎨 عرض مرفق: ${attachment.fileName}');
            return _buildAttachmentItem(attachment);
          }).toList(),
        );
      },
    );
  }

  /// عرض قائمة خيارات الرسالة
  void _showMessageOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.reply),
            title: const Text('رد'),
            onTap: () {
              Navigator.pop(context);
              if (widget.onReply != null) {
                widget.onReply!(widget.message);
              }
            },
          ),
          if (widget.isAdmin || widget.isCurrentUser)
            ListTile(
              leading: Icon(widget.message.isPinned
                  ? Icons.push_pin_outlined
                  : Icons.push_pin),
              title: Text(widget.message.isPinned ? 'إلغاء التثبيت' : 'تثبيت'),
              onTap: () {
                Navigator.pop(context);
                if (widget.message.isPinned) {
                  if (widget.onUnpin != null) {
                    widget.onUnpin!(widget.message);
                  } else {
                    // تنفيذ إلغاء التثبيت
                    debugPrint('إلغاء تثبيت الرسالة: ${widget.message.id}');
                  }
                } else {
                  if (widget.onPin != null) {
                    widget.onPin!(widget.message);
                  } else {
                    // تنفيذ التثبيت
                    debugPrint('تثبيت الرسالة: ${widget.message.id}');
                  }
                }
              },
            ),
          ListTile(
            leading: const Icon(Icons.flag),
            title: const Text('تعليم للمتابعة'),
            onTap: () {
              Navigator.pop(context);
              if (widget.onMarkForFollowUp != null) {
                widget.onMarkForFollowUp!(widget.message);
              }
            },
          ),
          if (widget.isCurrentUser)
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('تحرير'),
              onTap: () {
                Navigator.pop(context);
                if (widget.onEdit != null) {
                  widget.onEdit!(widget.message);
                }
              },
            ),
          if (widget.isCurrentUser || widget.isAdmin)
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('حذف', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                if (widget.onDelete != null) {
                  widget.onDelete!(widget.message);
                }
              },
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox(
        height: 50,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final senderName = _sender?.name ?? 'مستخدم غير معروف';

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: widget.isCurrentUser
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!widget.isCurrentUser && widget.showAvatar)
            CircleAvatar(
              radius: 16,
              backgroundImage: _sender?.profileImage != null
                  ? NetworkImage(_sender!.profileImage!)
                  : null,
              child: _sender?.profileImage == null
                  ? Text(senderName.isNotEmpty ? senderName[0] : '?')
                  : null,
            ),
          if (!widget.isCurrentUser && !widget.showAvatar)
            const SizedBox(width: 32),
          const SizedBox(width: 8),
          Flexible(
            child: Column(
              crossAxisAlignment: widget.isCurrentUser
                  ? CrossAxisAlignment.end
                  : CrossAxisAlignment.start,
              children: [
                // اسم المرسل (يظهر فقط إذا لم يكن المستخدم الحالي وكان يجب عرض الصورة الرمزية)
                if (!widget.isCurrentUser && widget.showAvatar)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Text(
                      senderName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),

                // الرسالة المرد عليها
                if (widget.message.replyToMessageId != null &&
                    _replyToMessage != null)
                  Container(
                    margin: const EdgeInsets.only(bottom: 4.0),
                    padding: const EdgeInsets.all(8.0),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'رد على ${_replyToUser?.name ?? 'مستخدم غير معروف'}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 10,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          _replyToMessage!.content,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.black87,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                // فقاعة الرسالة
                GestureDetector(
                  onLongPress: _showMessageOptions,
                  child: Container(
                    padding: const EdgeInsets.all(12.0),
                    decoration: BoxDecoration(
                      color: widget.isCurrentUser
                          ? AppColors.primary
                          : Colors.grey[200],
                      borderRadius: BorderRadius.circular(16.0),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // محتوى الرسالة
                        _buildMessageContent(),

                        const SizedBox(height: 4),

                        // الوقت وحالة القراءة
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              DateTimeFormatter.formatTime(
                                  DateTime.fromMillisecondsSinceEpoch(widget.message.createdAt * 1000)),
                              style: TextStyle(
                                fontSize: 10,
                                color: widget.isCurrentUser
                                    ? Colors.white70
                                    : Colors.black54,
                              ),
                            ),
                            if (widget.isCurrentUser)
                              Padding(
                                padding: const EdgeInsets.only(right: 4),
                                child: Icon(
                                  widget.message.isRead
                                      ? Icons.done_all
                                      : Icons.done,
                                  size: 12,
                                  color: widget.isCurrentUser
                                      ? Colors.white70
                                      : Colors.black54,
                                ),
                              ),
                            if (widget.message.isPinned)
                              Padding(
                                padding: const EdgeInsets.only(right: 4),
                                child: Icon(
                                  Icons.push_pin,
                                  size: 12,
                                  color: widget.isCurrentUser
                                      ? Colors.white70
                                      : Colors.black54,
                                ),
                              ),
                            if (widget.message.priority !=
                                MessagePriority.normal)
                              Padding(
                                padding: const EdgeInsets.only(right: 4),
                                child: Icon(
                                  Icons.priority_high,
                                  size: 12,
                                  color: widget.isCurrentUser
                                      ? Colors.white70
                                      : Colors.black54,
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // تفاعلات الرسالة
                MessageReactionsWidget(
                  messageId: widget.message.id.toString(),
                  currentUserId: widget.currentUserId,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر مرفق واحد
  Widget _buildAttachmentItem(MessageAttachment attachment) {
    if (_isImageFile(attachment.fileType)) {
      return _buildImageAttachment(attachment);
    } else {
      return _buildFileAttachment(attachment);
    }
  }

  /// بناء مرفق صورة
  Widget _buildImageAttachment(MessageAttachment attachment) {
    return GestureDetector(
      onTap: () => _downloadAttachment(attachment),
      child: Container(
        margin: const EdgeInsets.only(bottom: 4),
        constraints: BoxConstraints(
          maxWidth: 200,
          maxHeight: 200,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              Image.network(
                _getAttachmentUrl(attachment),
                fit: BoxFit.cover,
                width: double.infinity,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.broken_image, size: 48, color: Colors.grey),
                        SizedBox(height: 8),
                        Text(
                          'خطأ في تحميل الصورة',
                          style: TextStyle(color: Colors.grey[600], fontSize: 12),
                        ),
                      ],
                    ),
                  );
                },
              ),
              // طبقة شفافة للإشارة إلى أنها قابلة للنقر
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.black.withValues(alpha: 0.0),
                  ),
                ),
              ),
              // أيقونة صغيرة للإشارة إلى إمكانية الفتح
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.zoom_in,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء مرفق ملف عادي
  Widget _buildFileAttachment(MessageAttachment attachment) {
    return GestureDetector(
      onTap: () => _downloadAttachment(attachment),
      child: Container(
        margin: const EdgeInsets.only(bottom: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: widget.isCurrentUser ? Colors.white.withValues(alpha: 0.2) : Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: widget.isCurrentUser ? Colors.white.withValues(alpha: 0.3) : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: widget.isCurrentUser ? Colors.white.withValues(alpha: 0.2) : Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getFileIcon(attachment.fileType),
                size: 24,
                color: widget.isCurrentUser ? Colors.white : Colors.grey[700],
              ),
            ),
            const SizedBox(width: 12),
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    attachment.fileName,
                    style: TextStyle(
                      color: widget.isCurrentUser ? Colors.white : Colors.black,
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 2),
                  Text(
                    _formatFileSize(attachment.fileSize),
                    style: TextStyle(
                      color: widget.isCurrentUser ? Colors.white70 : Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              _getActionIcon(attachment.fileType),
              size: 20,
              color: widget.isCurrentUser ? Colors.white70 : Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  /// تحميل مرفقات الرسالة
  Future<List<MessageAttachment>> _loadMessageAttachments() async {
    try {
      debugPrint('🔄 بدء تحميل مرفقات الرسالة ${widget.message.id}');

      // استخدام API مباشرة لتجنب مشاكل الـ cache في Controller
      final apiService = MessageAttachmentsApiService();
      final attachments = await apiService.getAttachmentsByMessage(widget.message.id);

      debugPrint('✅ تم تحميل ${attachments.length} مرفق للرسالة ${widget.message.id}');

      // طباعة تفاصيل المرفقات للتشخيص
      for (var attachment in attachments) {
        debugPrint('📎 مرفق: ${attachment.fileName} - ${attachment.filePath}');
      }

      return attachments;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مرفقات الرسالة ${widget.message.id}: $e');
      return [];
    }
  }

  /// الحصول على أيقونة الملف حسب نوعه
  IconData _getFileIcon(String fileType) {
    if (fileType.startsWith('image/')) return Icons.image;
    if (fileType.startsWith('video/')) return Icons.videocam;
    if (fileType.startsWith('audio/')) return Icons.audiotrack;
    if (fileType.contains('pdf')) return Icons.picture_as_pdf;
    if (fileType.contains('word') || fileType.contains('document')) return Icons.description;
    if (fileType.contains('excel') || fileType.contains('spreadsheet')) return Icons.table_chart;
    if (fileType.contains('powerpoint') || fileType.contains('presentation')) return Icons.slideshow;
    if (fileType.contains('zip') || fileType.contains('rar')) return Icons.archive;
    return Icons.insert_drive_file;
  }

  /// الحصول على أيقونة الإجراء حسب نوع الملف
  IconData _getActionIcon(String fileType) {
    if (_isImageFile(fileType)) return Icons.visibility;
    if (_isPdfFile(fileType)) return Icons.open_in_new;
    if (_isVideoFile(fileType)) return Icons.play_arrow;
    if (_isAudioFile(fileType)) return Icons.play_arrow;
    return Icons.download;
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// فتح/تحميل المرفق
  void _downloadAttachment(MessageAttachment attachment) {
    if (_isImageFile(attachment.fileType)) {
      _openImageViewer(attachment);
    } else if (_isPdfFile(attachment.fileType)) {
      _openPdfViewer(attachment);
    } else if (_isVideoFile(attachment.fileType)) {
      _openVideoPlayer(attachment);
    } else if (_isAudioFile(attachment.fileType)) {
      _openAudioPlayer(attachment);
    } else {
      _downloadFile(attachment);
    }
  }

  /// التحقق من أن الملف صورة
  bool _isImageFile(String fileType) {
    return fileType.startsWith('image/') ||
           fileType.toLowerCase().contains('png') ||
           fileType.toLowerCase().contains('jpg') ||
           fileType.toLowerCase().contains('jpeg') ||
           fileType.toLowerCase().contains('gif') ||
           fileType.toLowerCase().contains('webp');
  }

  /// التحقق من أن الملف PDF
  bool _isPdfFile(String fileType) {
    return fileType.contains('pdf') || fileType.toLowerCase().contains('pdf');
  }

  /// التحقق من أن الملف فيديو
  bool _isVideoFile(String fileType) {
    return fileType.startsWith('video/') ||
           fileType.toLowerCase().contains('mp4') ||
           fileType.toLowerCase().contains('avi') ||
           fileType.toLowerCase().contains('mov');
  }

  /// التحقق من أن الملف صوتي
  bool _isAudioFile(String fileType) {
    return fileType.startsWith('audio/') ||
           fileType.toLowerCase().contains('mp3') ||
           fileType.toLowerCase().contains('wav') ||
           fileType.toLowerCase().contains('m4a');
  }

  /// فتح عارض الصور
  void _openImageViewer(MessageAttachment attachment) {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.black,
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                child: Image.network(
                  _getAttachmentUrl(attachment),
                  fit: BoxFit.contain,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error, color: Colors.white, size: 48),
                          SizedBox(height: 16),
                          Text(
                            'خطأ في تحميل الصورة',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
            Positioned(
              top: 40,
              right: 20,
              child: IconButton(
                icon: Icon(Icons.close, color: Colors.white, size: 30),
                onPressed: () => Get.back(),
              ),
            ),
            Positioned(
              top: 40,
              left: 20,
              child: IconButton(
                icon: Icon(Icons.download, color: Colors.white, size: 30),
                onPressed: () => _downloadFile(attachment),
              ),
            ),
            Positioned(
              bottom: 20,
              left: 20,
              right: 20,
              child: Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  attachment.fileName,
                  style: TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// فتح عارض PDF
  void _openPdfViewer(MessageAttachment attachment) {
    Get.snackbar(
      'PDF',
      'فتح ملف PDF: ${attachment.fileName}',
      snackPosition: SnackPosition.BOTTOM,
      duration: Duration(seconds: 2),
    );
    // TODO: تنفيذ عارض PDF
    _downloadFile(attachment);
  }

  /// فتح مشغل الفيديو
  void _openVideoPlayer(MessageAttachment attachment) {
    Get.snackbar(
      'فيديو',
      'فتح فيديو: ${attachment.fileName}',
      snackPosition: SnackPosition.BOTTOM,
      duration: Duration(seconds: 2),
    );
    // TODO: تنفيذ مشغل الفيديو
    _downloadFile(attachment);
  }

  /// فتح مشغل الصوت
  void _openAudioPlayer(MessageAttachment attachment) {
    Get.snackbar(
      'صوت',
      'تشغيل ملف صوتي: ${attachment.fileName}',
      snackPosition: SnackPosition.BOTTOM,
      duration: Duration(seconds: 2),
    );
    // TODO: تنفيذ مشغل الصوت
    _downloadFile(attachment);
  }

  /// تحميل الملف
  void _downloadFile(MessageAttachment attachment) {
    Get.snackbar(
      'تحميل',
      'جاري تحميل ${attachment.fileName}...',
      snackPosition: SnackPosition.BOTTOM,
      duration: Duration(seconds: 3),
    );
    // TODO: تنفيذ تحميل الملف الفعلي
  }

  /// الحصول على رابط المرفق
  String _getAttachmentUrl(MessageAttachment attachment) {
    // استخدام عنوان الخادم الديناميكي من إعدادات التطبيق
    final url = '${ApiConfig.baseUrl}${attachment.filePath}';
    debugPrint('🔗 رابط المرفق: $url');
    return url;
  }

  /// بناء رسالة صورة
  Widget _buildImageMessage() {
    return _buildTextMessage(); // مؤقت
  }

  /// بناء رسالة صوتية
  Widget _buildVoiceMessage() {
    return _buildTextMessage(); // مؤقت
  }

  /// بناء رسالة فيديو
  Widget _buildVideoMessage() {
    return _buildTextMessage(); // مؤقت
  }

  /// بناء رسالة موقع
  Widget _buildLocationMessage() {
    return _buildTextMessage(); // مؤقت
  }
}
