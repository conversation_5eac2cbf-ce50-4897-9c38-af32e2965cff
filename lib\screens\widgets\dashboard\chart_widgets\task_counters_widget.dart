import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/dashboard_widget_model.dart';
import 'package:get/get.dart';


import '../../../../controllers/task_controller.dart';
import '../../../../models/task_status_enum.dart';

/// عنصر عدادات المهام
///
/// يعرض عدادات للمهام حسب الحالة
///
/// ملاحظة: يوصى بتوحيد هذا المكون مع مكونات المخططات الأخرى في مجلد widgets/charts
/// لتوحيد واجهة المستخدم وتقليل التكرار في الكود.
/// راجع ملف DASHBOARD_REFACTORING.md للمزيد من المعلومات.
class TaskCountersWidget extends StatelessWidget {
  /// عنصر لوحة المعلومات
  final DashboardWidget widget;

  /// إعدادات العنصر
  final Map<String, dynamic> settings;

  /// دالة يتم استدعاؤها عند تحديث الإعدادات
  final Function(DashboardWidget, Map<String, dynamic>)? onSettingsUpdated;

  /// ما إذا كان عرض تفاصيل
  final bool isDetailView;

  const TaskCountersWidget({
    super.key,
    required this.widget,
    required this.settings,
    this.onSettingsUpdated,
    this.isDetailView = false,
  });

  @override
  Widget build(BuildContext context) {
    // تحكم المهام
    final TaskController taskController = Get.find<TaskController>();

    return Obx(() {
      final tasks = taskController.allTasks;

      if (tasks.isEmpty) {
        return Center(
          child: Text(
            'لا توجد مهام',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
        );
      }

      // حساب عدد المهام حسب الحالة
      int totalTasks = tasks.length;
      int completedTasks =
          tasks.where((task) => task.status == TaskStatus.completed.stringValue).length;
      int inProgressTasks =
          tasks.where((task) => task.status == TaskStatus.inProgress.stringValue).length;
      int pendingTasks =
          tasks.where((task) => task.status == TaskStatus.pending.stringValue).length;
      int waitingForInfoTasks = tasks
          .where((task) => task.status == TaskStatus.waitingForInfo.stringValue)
          .length;
      int cancelledTasks =
          tasks.where((task) => task.status == TaskStatus.cancelled.stringValue).length;
      int newsTasks =
          tasks.where((task) => task.status == TaskStatus.news.stringValue).length;

      // حساب المهام المتأخرة
      int overdueTasks = tasks.where((task) {
        final dueDate = task.dueDate != null
            ? DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000)
            : null;
        return dueDate != null &&
            dueDate.isBefore(DateTime.now()) &&
            task.status != TaskStatus.completed.stringValue &&
            task.status != TaskStatus.cancelled.stringValue;
      }).length;

      // تحديد العناصر التي سيتم عرضها
      final List<Widget> counters = [];

      if (settings['showTotal'] == true) {
        counters.add(
          _buildCounter(
            context,
            'إجمالي المهام',
            totalTasks,
            Colors.blue,
            Icons.assignment,
          ),
        );
      }

      if (settings['showCompleted'] == true) {
        counters.add(
          _buildCounter(
            context,
            'المهام المكتملة',
            completedTasks,
            Colors.green,
            Icons.check_circle_outline,
          ),
        );
      }

      if (settings['showInProgress'] == true) {
        counters.add(
          _buildCounter(
            context,
            'قيد التنفيذ',
            inProgressTasks,
            Colors.orange,
            Icons.play_circle_outline,
          ),
        );
      }

      if (settings['showOverdue'] == true) {
        counters.add(
          _buildCounter(
            context,
            'المهام المتأخرة',
            overdueTasks,
            Colors.red,
            Icons.warning_amber_outlined,
          ),
        );
      }

      if (settings['showNotStarted'] == true) {
        counters.add(
          _buildCounter(
            context,
            'قيد الانتظار',
            pendingTasks,
            Colors.grey,
            Icons.hourglass_empty,
          ),
        );
      }

      if (settings['showOnHold'] == true) {
        counters.add(
          _buildCounter(
            context,
            'في انتظار معلومات',
            waitingForInfoTasks,
            Colors.purple,
            Icons.help_outline,
          ),
        );
      }

      if (settings['showNew'] == true) {
        counters.add(
          _buildCounter(
            context,
            'جديدة',
            newsTasks,
            Colors.teal,
            Icons.fiber_new,
          ),
        );
      }

      if (settings['showCancelled'] == true) {
        counters.add(
          _buildCounter(
            context,
            'ملغاة',
            cancelledTasks,
            Colors.red.shade300,
            Icons.cancel_outlined,
          ),
        );
      }

      // إضافة عداد نسبة الإكمال
      if (settings['showCompletionRate'] == true) {
        final completionRate = totalTasks > 0
            ? (completedTasks / totalTasks * 100).toStringAsFixed(1)
            : '0';
        counters.add(
          _buildCounter(
            context,
            'نسبة الإكمال',
            '$completionRate%',
            Colors.teal,
            Icons.pie_chart_outline,
          ),
        );
      }

      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: isDetailView
            ? GridView.count(
                crossAxisCount: 4,
                childAspectRatio: 1.5,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                shrinkWrap: true, // إضافة هذه الخاصية لمنع التمدد غير المحدود
                physics:
                    const NeverScrollableScrollPhysics(), // منع التمرير داخل GridView
                children: counters,
              )
            : SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: counters,
                ),
              ),
      );
    });
  }

  /// بناء عداد
  Widget _buildCounter(
    BuildContext context,
    String title,
    dynamic value,
    Color color,
    IconData icon,
  ) {
    final textColor = Theme.of(context).brightness == Brightness.dark
        ? Colors.white
        : Colors.black87;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(26), // 0.1 * 255 = 26
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withAlpha(77), // 0.3 * 255 = 77
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value is int ? value.toString() : value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: textColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
