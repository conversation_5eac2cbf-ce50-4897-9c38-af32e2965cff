import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/dashboard_widget_model.dart';
import 'package:get/get.dart';
import '../../../../models/task_status_enum.dart';


import '../../../../controllers/task_controller.dart';

/// عنصر مؤشر الأداء الرئيسي
///
/// يعرض مؤشر أداء رئيسي (KPI)
///
/// ملاحظة: يوصى بتوحيد هذا المكون مع مكونات المخططات الأخرى في مجلد widgets/charts
/// لتوحيد واجهة المستخدم وتقليل التكرار في الكود.
/// راجع ملف DASHBOARD_REFACTORING.md للمزيد من المعلومات.
class KpiWidget extends StatelessWidget {
  /// عنصر لوحة المعلومات
  final DashboardWidget widget;

  /// إعدادات العنصر
  final Map<String, dynamic> settings;

  /// دالة يتم استدعاؤها عند تحديث الإعدادات
  final Function(DashboardWidget, Map<String, dynamic>)? onSettingsUpdated;

  /// ما إذا كان عرض تفاصيل
  final bool isDetailView;

  const KpiWidget({
    Key? key,
    required this.widget,
    required this.settings,
    this.onSettingsUpdated,
    this.isDetailView = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // تحكم المهام
    final TaskController taskController = Get.find<TaskController>();

    return Obx(() {
      final tasks = taskController.allTasks;

      if (tasks.isEmpty) {
        return Center(
          child: Text(
            'لا توجد مهام',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
        );
      }

      // تحديد نوع مؤشر الأداء
      final kpiType = settings['kpiType'] ?? 'completionRate';

      // تحديد نطاق الوقت
      final timeRange = settings['timeRange'] ?? 'week';
      final DateTime now = DateTime.now();
      DateTime startDate;

      switch (timeRange) {
        case 'week':
          startDate = now.subtract(const Duration(days: 7));
          break;
        case 'month':
          startDate = DateTime(now.year, now.month - 1, now.day);
          break;
        case 'quarter':
          startDate = DateTime(now.year, now.month - 3, now.day);
          break;
        case 'year':
          startDate = DateTime(now.year - 1, now.month, now.day);
          break;
        default:
          startDate = DateTime(now.year, now.month - 1, now.day);
      }

      // تحديد الفترة السابقة للمقارنة
      final DateTime previousStartDate;
      final DateTime previousEndDate = startDate;

      switch (timeRange) {
        case 'week':
          previousStartDate = previousEndDate.subtract(const Duration(days: 7));
          break;
        case 'month':
          previousStartDate = DateTime(previousEndDate.year,
              previousEndDate.month - 1, previousEndDate.day);
          break;
        case 'quarter':
          previousStartDate = DateTime(previousEndDate.year,
              previousEndDate.month - 3, previousEndDate.day);
          break;
        case 'year':
          previousStartDate = DateTime(previousEndDate.year - 1,
              previousEndDate.month, previousEndDate.day);
          break;
        default:
          previousStartDate = DateTime(previousEndDate.year,
              previousEndDate.month - 1, previousEndDate.day);
      }

      // تصفية المهام حسب الفترة الزمنية
      final currentPeriodTasks = tasks.where((task) {
        final createdAt = DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000);
        return createdAt.isAfter(startDate) &&
            createdAt.isBefore(now);
      }).toList();

      final previousPeriodTasks = tasks.where((task) {
        final createdAt = DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000);
        return createdAt.isAfter(previousStartDate) &&
            createdAt.isBefore(previousEndDate);
      }).toList();

      // حساب قيمة مؤشر الأداء
      double kpiValue = 0;
      double previousKpiValue = 0;
      String kpiTitle = '';
      IconData kpiIcon = Icons.trending_up;
      Color kpiColor = Colors.green;

      switch (kpiType) {
        case 'completionRate':
          kpiTitle = 'معدل الإكمال';
          kpiIcon = Icons.check_circle_outline;
          kpiColor = Colors.green;

          final completedTasks = currentPeriodTasks
              .where((task) => task.status == TaskStatus.completed.id)
              .length;
          kpiValue = currentPeriodTasks.isNotEmpty
              ? (completedTasks / currentPeriodTasks.length) * 100
              : 0;

          final previousCompletedTasks = previousPeriodTasks
              .where((task) => task.status == TaskStatus.completed.id)
              .length;
          previousKpiValue = previousPeriodTasks.isNotEmpty
              ? (previousCompletedTasks / previousPeriodTasks.length) * 100
              : 0;
          break;

        case 'overdueRate':
          kpiTitle = 'معدل التأخير';
          kpiIcon = Icons.warning_amber_outlined;
          kpiColor = Colors.orange;

          final overdueTasks = currentPeriodTasks.where((task) {
            final dueDate = task.dueDate != null
                ? DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000)
                : null;
            return dueDate != null &&
                dueDate.isBefore(now) &&
                task.status != TaskStatus.completed.id &&
                task.status != TaskStatus.cancelled.id;
          }).length;
          kpiValue = currentPeriodTasks.isNotEmpty
              ? (overdueTasks / currentPeriodTasks.length) * 100
              : 0;

          final previousOverdueTasks = previousPeriodTasks.where((task) {
            final dueDate = task.dueDate != null
                ? DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000)
                : null;
            return dueDate != null &&
                dueDate.isBefore(previousEndDate) &&
                task.status != TaskStatus.completed.id &&
                task.status != TaskStatus.cancelled.id;
          }).length;
          previousKpiValue = previousPeriodTasks.isNotEmpty
              ? (previousOverdueTasks / previousPeriodTasks.length) * 100
              : 0;
          break;

        case 'taskCount':
          kpiTitle = 'عدد المهام';
          kpiIcon = Icons.assignment;
          kpiColor = Colors.blue;

          kpiValue = currentPeriodTasks.length.toDouble();
          previousKpiValue = previousPeriodTasks.length.toDouble();
          break;

        case 'completedTaskCount':
          kpiTitle = 'المهام المكتملة';
          kpiIcon = Icons.done_all;
          kpiColor = Colors.green;

          kpiValue = currentPeriodTasks
              .where((task) => task.status == TaskStatus.completed.id)
              .length
              .toDouble();
          previousKpiValue = previousPeriodTasks
              .where((task) => task.status == TaskStatus.completed.id)
              .length
              .toDouble();
          break;
      }

      // حساب نسبة التغيير
      final double changePercentage = previousKpiValue > 0
          ? ((kpiValue - previousKpiValue) / previousKpiValue) * 100
          : (kpiValue > 0 ? 100 : 0);

      // تحديد اتجاه التغيير
      final bool isPositiveChange = kpiType == 'overdueRate'
          ? changePercentage < 0
          : changePercentage > 0;
      final IconData changeIcon =
          isPositiveChange ? Icons.trending_up : Icons.trending_down;
      final Color changeColor = isPositiveChange ? Colors.green : Colors.red;

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: kpiColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: kpiColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (settings['showIcon'] == true) ...[
              Icon(
                kpiIcon,
                color: kpiColor,
                size: 32,
              ),
              const SizedBox(height: 8),
            ],
            Text(
              kpiType == 'taskCount' || kpiType == 'completedTaskCount'
                  ? kpiValue.toInt().toString()
                  : '${kpiValue.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: kpiColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              kpiTitle,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            if (settings['showChange'] == true) ...[
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    changeIcon,
                    color: changeColor,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${changePercentage.abs().toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: changeColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                _getTimeRangeText(timeRange),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      );
    });
  }

  /// الحصول على نص الفترة الزمنية
  String _getTimeRangeText(String timeRange) {
    switch (timeRange) {
      case 'week':
        return 'مقارنة بالأسبوع السابق';
      case 'month':
        return 'مقارنة بالشهر السابق';
      case 'quarter':
        return 'مقارنة بالربع السابق';
      case 'year':
        return 'مقارنة بالعام السابق';
      default:
        return 'مقارنة بالفترة السابقة';
    }
  }
}
