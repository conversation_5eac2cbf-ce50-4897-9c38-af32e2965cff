import '../../models/permission_template_models.dart';
import 'api_service.dart';

/// خدمة API لإدارة قوالب الصلاحيات
class PermissionTemplatesApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع قوالب الصلاحيات
  Future<List<PermissionTemplate>> getPermissionTemplates() async {
    try {
      final response = await _apiService.get('/api/PermissionTemplates');
      return _apiService.handleResponse<List<PermissionTemplate>>(
        response,
        (json) => (json as List)
            .map((item) => PermissionTemplate.fromJson(item))
            .toList(),
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على قوالب الصلاحيات: $e');
    }
  }

  /// الحصول على قالب صلاحيات محدد
  Future<PermissionTemplate> getPermissionTemplate(int id) async {
    try {
      final response = await _apiService.get('/api/PermissionTemplates/$id');
      return _apiService.handleResponse<PermissionTemplate>(
        response,
        (json) => PermissionTemplate.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على القالب: $e');
    }
  }

  /// إنشاء قالب صلاحيات جديد
  Future<PermissionTemplate> createPermissionTemplate(CreatePermissionTemplateRequest request) async {
    try {
      final response = await _apiService.post('/api/PermissionTemplates', request.toJson());
      return _apiService.handleResponse<PermissionTemplate>(
        response,
        (json) => PermissionTemplate.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء القالب: $e');
    }
  }

  /// تحديث قالب صلاحيات
  Future<void> updatePermissionTemplate(int id, UpdatePermissionTemplateRequest request) async {
    try {
      final response = await _apiService.put('/api/PermissionTemplates/$id', request.toJson());
      _apiService.handleResponse<void>(response, (json) {});
    } catch (e) {
      throw Exception('خطأ في تحديث القالب: $e');
    }
  }

  /// حذف قالب صلاحيات
  Future<void> deletePermissionTemplate(int id) async {
    try {
      final response = await _apiService.delete('/api/PermissionTemplates/$id');
      _apiService.handleResponse<void>(response, (json) {});
    } catch (e) {
      throw Exception('خطأ في حذف القالب: $e');
    }
  }

  /// تطبيق قالب على مستخدم
  Future<void> applyTemplateToUser(ApplyTemplateToUserRequest request) async {
    try {
      final response = await _apiService.post('/api/PermissionTemplates/apply', request.toJson());
      _apiService.handleResponse<void>(response, (json) {});
    } catch (e) {
      throw Exception('خطأ في تطبيق القالب: $e');
    }
  }

  /// الحصول على القوالب الافتراضية
  Future<List<PermissionTemplate>> getDefaultTemplates() async {
    try {
      final allTemplates = await getPermissionTemplates();
      return allTemplates.where((template) => template.isDefault).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على القوالب الافتراضية: $e');
    }
  }

  /// الحصول على القوالب المخصصة
  Future<List<PermissionTemplate>> getCustomTemplates() async {
    try {
      final allTemplates = await getPermissionTemplates();
      return allTemplates.where((template) => !template.isDefault).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على القوالب المخصصة: $e');
    }
  }

  /// البحث في القوالب
  Future<List<PermissionTemplate>> searchTemplates(String query) async {
    try {
      final allTemplates = await getPermissionTemplates();
      return allTemplates.where((template) =>
        template.name.toLowerCase().contains(query.toLowerCase()) ||
        (template.description?.toLowerCase().contains(query.toLowerCase()) ?? false)
      ).toList();
    } catch (e) {
      throw Exception('خطأ في البحث في القوالب: $e');
    }
  }

  /// الحصول على القوالب حسب النوع
  Future<List<PermissionTemplate>> getTemplatesByType(String type) async {
    try {
      final allTemplates = await getPermissionTemplates();
      return allTemplates.where((template) => template.type == type).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على القوالب حسب النوع: $e');
    }
  }

  /// نسخ قالب موجود
  Future<PermissionTemplate> duplicateTemplate(int templateId, String newName, int createdBy) async {
    try {
      // الحصول على القالب الأصلي
      final originalTemplate = await getPermissionTemplate(templateId);
      
      // إنشاء طلب قالب جديد
      final request = CreatePermissionTemplateRequest(
        name: newName,
        description: '${originalTemplate.description} (نسخة)',
        type: 'custom',
        color: originalTemplate.color,
        icon: originalTemplate.icon,
        permissionIds: originalTemplate.templateItems
            .where((item) => item.isEnabled)
            .map((item) => item.permissionId)
            .toList(),
        createdBy: createdBy,
      );

      return await createPermissionTemplate(request);
    } catch (e) {
      throw Exception('خطأ في نسخ القالب: $e');
    }
  }

  /// إحصائيات القوالب
  Future<TemplateStatistics> getTemplateStatistics() async {
    try {
      final templates = await getPermissionTemplates();
      
      final defaultCount = templates.where((t) => t.isDefault).length;
      final customCount = templates.where((t) => !t.isDefault).length;
      final activeCount = templates.where((t) => t.isActive).length;
      final inactiveCount = templates.where((t) => !t.isActive).length;

      final typeGroups = <String, int>{};
      for (final template in templates) {
        typeGroups[template.type] = (typeGroups[template.type] ?? 0) + 1;
      }

      return TemplateStatistics(
        totalTemplates: templates.length,
        defaultTemplates: defaultCount,
        customTemplates: customCount,
        activeTemplates: activeCount,
        inactiveTemplates: inactiveCount,
        templatesByType: typeGroups,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات القوالب: $e');
    }
  }
}

/// إحصائيات القوالب
class TemplateStatistics {
  final int totalTemplates;
  final int defaultTemplates;
  final int customTemplates;
  final int activeTemplates;
  final int inactiveTemplates;
  final Map<String, int> templatesByType;

  TemplateStatistics({
    required this.totalTemplates,
    required this.defaultTemplates,
    required this.customTemplates,
    required this.activeTemplates,
    required this.inactiveTemplates,
    required this.templatesByType,
  });
}
