import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:intl/intl.dart';
import '../../../constants/app_colors.dart';

/// نموذج بيانات المخطط المساحي
class AreaChartData {
  /// النقطة على المحور السيني (عادة الوقت)
  final String x;
  
  /// القيمة على المحور الصادي
  final double y;
  
  /// بيانات إضافية (اختياري)
  final Map<String, dynamic>? metadata;

  const AreaChartData({
    required this.x,
    required this.y,
    this.metadata,
  });
}

/// نموذج سلسلة بيانات المخطط المساحي
class AreaChartSeries {
  /// اسم السلسلة
  final String name;
  
  /// بيانات السلسلة
  final List<AreaChartData> data;
  
  /// لون السلسلة
  final Color color;
  
  /// شفافية المنطقة المملوءة (0.0 - 1.0)
  final double opacity;
  
  /// سمك الخط
  final double strokeWidth;

  const AreaChartSeries({
    required this.name,
    required this.data,
    required this.color,
    this.opacity = 0.3,
    this.strokeWidth = 2.0,
  });
}

/// مكون المخطط المساحي المحسن
///
/// يوفر هذا المكون مخططًا مساحيًا متقدمًا لعرض البيانات الزمنية
/// مفيد لإظهار الاتجاهات والتغيرات عبر الزمن مع التأكيد على الحجم
class EnhancedAreaChart extends StatefulWidget {
  /// عنوان المخطط
  final String title;

  /// سلاسل البيانات
  final List<AreaChartSeries> series;

  /// عنوان المحور السيني
  final String? xAxisTitle;

  /// عنوان المحور الصادي
  final String? yAxisTitle;

  /// إظهار وسائل الإيضاح
  final bool showLegend;

  /// إظهار الشبكة
  final bool showGrid;

  /// إظهار القيم على النقاط
  final bool showDataLabels;

  /// تمكين التكبير والتصغير
  final bool enableZooming;

  /// تمكين التحريك
  final bool enablePanning;

  /// ارتفاع المخطط
  final double? height;

  /// عرض المخطط
  final double? width;

  /// ألوان مخصصة للسلاسل
  final List<Color>? customColors;

  /// إنشاء مخطط مساحي محسن
  const EnhancedAreaChart({
    super.key,
    required this.title,
    required this.series,
    this.xAxisTitle,
    this.yAxisTitle,
    this.showLegend = true,
    this.showGrid = true,
    this.showDataLabels = false,
    this.enableZooming = true,
    this.enablePanning = true,
    this.height,
    this.width,
    this.customColors,
  });

  @override
  State<EnhancedAreaChart> createState() => _EnhancedAreaChartState();
}

class _EnhancedAreaChartState extends State<EnhancedAreaChart> {
  late TooltipBehavior _tooltipBehavior;
  late ZoomPanBehavior _zoomPanBehavior;

  @override
  void initState() {
    super.initState();
    _tooltipBehavior = TooltipBehavior(enable: true);
    _zoomPanBehavior = ZoomPanBehavior(
      enableDoubleTapZooming: widget.enableZooming,
      enablePinching: widget.enableZooming,
      enablePanning: widget.enablePanning,
      enableSelectionZooming: widget.enableZooming,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      width: widget.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (widget.title.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                widget.title,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
          Expanded(
            child: _buildChart(),
          ),
        ],
      ),
    );
  }

  Widget _buildChart() {
    if (widget.series.isEmpty) {
      return _buildNoDataWidget();
    }

    return SfCartesianChart(
      tooltipBehavior: _tooltipBehavior,
      zoomPanBehavior: _zoomPanBehavior,
      legend: Legend(
        isVisible: widget.showLegend,
        position: LegendPosition.bottom,
        overflowMode: LegendItemOverflowMode.wrap,
      ),
      primaryXAxis: CategoryAxis(
        title: widget.xAxisTitle != null
            ? AxisTitle(text: widget.xAxisTitle!)
            : AxisTitle(text: ''),
        majorGridLines: widget.showGrid
            ? const MajorGridLines(width: 0.5)
            : const MajorGridLines(width: 0),
        labelRotation: -45,
      ),
      primaryYAxis: NumericAxis(
        title: widget.yAxisTitle != null
            ? AxisTitle(text: widget.yAxisTitle!)
            : AxisTitle(text: ''),
        majorGridLines: widget.showGrid
            ? const MajorGridLines(width: 0.5)
            : const MajorGridLines(width: 0),
        numberFormat: NumberFormat.compact(),
      ),
      series: _buildAreaSeries(),
    );
  }

  List<AreaSeries<AreaChartData, String>> _buildAreaSeries() {
    final List<AreaSeries<AreaChartData, String>> seriesList = [];

    for (int i = 0; i < widget.series.length; i++) {
      final series = widget.series[i];
      final color = widget.customColors != null && i < widget.customColors!.length
          ? widget.customColors![i]
          : series.color;

      seriesList.add(
        AreaSeries<AreaChartData, String>(
          name: series.name,
          dataSource: series.data,
          xValueMapper: (AreaChartData data, _) => data.x,
          yValueMapper: (AreaChartData data, _) => data.y,
          color: color.withValues(alpha: series.opacity),
          borderColor: color,
          borderWidth: series.strokeWidth,
          dataLabelSettings: DataLabelSettings(
            isVisible: widget.showDataLabels,
            labelAlignment: ChartDataLabelAlignment.auto,
          ),
          enableTooltip: true,
          markerSettings: const MarkerSettings(
            isVisible: true,
            height: 4,
            width: 4,
            shape: DataMarkerType.circle,
          ),
        ),
      );
    }

    return seriesList;
  }

  Widget _buildNoDataWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min, // 🚀 إصلاح: منع overflow
        children: [
          Icon(
            Icons.area_chart,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات للعرض',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تأكد من وجود بيانات صحيحة للمخطط المساحي',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// دالة مساعدة لإنشاء بيانات مخطط مساحي من Map
List<AreaChartSeries> createAreaSeriesFromMap(Map<String, dynamic> data) {
  final List<AreaChartSeries> series = [];

  if (data.containsKey('series') && data['series'] is Map) {
    final Map<String, dynamic> seriesData = data['series'];
    final List<String> categories = data['categories'] ?? [];
    
    int colorIndex = 0;
    final List<Color> defaultColors = [
      AppColors.primary,
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];

    seriesData.forEach((seriesName, values) {
      if (values is List && categories.isNotEmpty) {
        final List<AreaChartData> chartData = [];
        
        for (int i = 0; i < categories.length && i < values.length; i++) {
          chartData.add(AreaChartData(
            x: categories[i],
            y: (values[i] as num).toDouble(),
          ));
        }

        if (chartData.isNotEmpty) {
          series.add(AreaChartSeries(
            name: seriesName,
            data: chartData,
            color: defaultColors[colorIndex % defaultColors.length],
            opacity: 0.3,
            strokeWidth: 2.0,
          ));
          colorIndex++;
        }
      }
    });
  }

  return series;
}
