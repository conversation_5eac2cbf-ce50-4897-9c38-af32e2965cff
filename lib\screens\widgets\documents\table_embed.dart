import 'dart:convert';

/// فئة TableEmbed لتمثيل الجداول كـ embed مخصص في Flutter Quill
/// تدعم إنشاء وتحرير الجداول الحقيقية بدلاً من النص المنسق
class TableEmbed {
  /// نوع الـ embed للجداول
  static const String tableType = 'table';

  /// البيانات الخام للجدول
  final String data;

  /// إنشاء TableEmbed من قيمة نصية
  TableEmbed(this.data);

  /// إنشاء TableEmbed من بيانات الجدول
  static TableEmbed fromTableData({
    required List<List<String>> data,
    bool hasHeader = true,
    String? title,
    Map<String, dynamic>? styling,
  }) {
    final tableConfig = {
      'data': data,
      'rows': data.length,
      'columns': data.isNotEmpty ? data[0].length : 0,
      'hasHeader': hasHeader,
      'title': title ?? 'جدول البيانات',
      'styling': styling ?? _getDefaultStyling(),
      'createdAt': DateTime.now().millisecondsSinceEpoch,
      'version': '1.0',
    };

    return TableEmbed(jsonEncode(tableConfig));
  }

  /// إنشاء TableEmbed فارغ بأبعاد محددة
  static TableEmbed createEmpty({
    required int rows,
    required int columns,
    bool hasHeader = true,
    String? title,
  }) {
    final data = List.generate(
      rows,
      (rowIndex) => List.generate(
        columns,
        (colIndex) {
          if (hasHeader && rowIndex == 0) {
            return 'العمود ${colIndex + 1}';
          }
          return '';
        },
      ),
    );

    return fromTableData(
      data: data,
      hasHeader: hasHeader,
      title: title,
    );
  }

  /// الحصول على بيانات الجدول
  Map<String, dynamic> get tableData {
    try {
      return jsonDecode(data) as Map<String, dynamic>;
    } catch (e) {
      // في حالة فشل التحليل، إرجاع جدول افتراضي
      return _getDefaultTableData();
    }
  }

  /// الحصول على بيانات الخلايا
  List<List<String>> get cellData {
    final tableConfig = tableData;
    final rawData = tableConfig['data'] as List<dynamic>?;

    if (rawData == null) return [['']];

    return rawData.map((row) {
      if (row is List) {
        return row.map((cell) => cell?.toString() ?? '').toList();
      }
      return [''];
    }).toList();
  }

  /// الحصول على عدد الصفوف
  int get rows => tableData['rows'] as int? ?? 1;

  /// الحصول على عدد الأعمدة
  int get columns => tableData['columns'] as int? ?? 1;

  /// هل يحتوي على رأس
  bool get hasHeader => tableData['hasHeader'] as bool? ?? true;

  /// عنوان الجدول
  String get title => tableData['title'] as String? ?? 'جدول البيانات';

  /// تنسيق الجدول
  Map<String, dynamic> get styling => 
      tableData['styling'] as Map<String, dynamic>? ?? _getDefaultStyling();

  /// تحديث بيانات الجدول
  TableEmbed updateData(List<List<String>> newData) {
    final currentConfig = Map<String, dynamic>.from(tableData);
    currentConfig['data'] = newData;
    currentConfig['rows'] = newData.length;
    currentConfig['columns'] = newData.isNotEmpty ? newData[0].length : 0;

    return TableEmbed(jsonEncode(currentConfig));
  }

  /// تحديث عنوان الجدول
  TableEmbed updateTitle(String newTitle) {
    final currentConfig = Map<String, dynamic>.from(tableData);
    currentConfig['title'] = newTitle;

    return TableEmbed(jsonEncode(currentConfig));
  }

  /// تحديث تنسيق الجدول
  TableEmbed updateStyling(Map<String, dynamic> newStyling) {
    final currentConfig = Map<String, dynamic>.from(tableData);
    currentConfig['styling'] = newStyling;

    return TableEmbed(jsonEncode(currentConfig));
  }

  /// إضافة صف جديد
  TableEmbed addRow([List<String>? rowData]) {
    final currentData = cellData;
    final newRow = rowData ?? List.filled(columns, '');

    // التأكد من أن الصف الجديد له نفس عدد الأعمدة
    while (newRow.length < columns) {
      newRow.add('');
    }
    if (newRow.length > columns) {
      newRow.removeRange(columns, newRow.length);
    }

    currentData.add(newRow);
    return updateData(currentData);
  }

  /// إضافة عمود جديد
  TableEmbed addColumn([String? columnHeader]) {
    final currentData = cellData;

    for (int i = 0; i < currentData.length; i++) {
      if (hasHeader && i == 0) {
        currentData[i].add(columnHeader ?? 'عمود جديد');
      } else {
        currentData[i].add('');
      }
    }

    return updateData(currentData);
  }

  /// حذف صف
  TableEmbed removeRow(int rowIndex) {
    final currentData = cellData;

    if (rowIndex >= 0 && rowIndex < currentData.length && currentData.length > 1) {
      currentData.removeAt(rowIndex);
    }

    return updateData(currentData);
  }

  /// حذف عمود
  TableEmbed removeColumn(int columnIndex) {
    final currentData = cellData;

    if (columnIndex >= 0 && columnIndex < columns && columns > 1) {
      for (final row in currentData) {
        if (columnIndex < row.length) {
          row.removeAt(columnIndex);
        }
      }
    }

    return updateData(currentData);
  }

  /// التحقق من صحة بيانات الجدول
  bool get isValid {
    try {
      final config = tableData;
      final data = config['data'] as List<dynamic>?;
      
      return data != null && 
             data.isNotEmpty && 
             config['rows'] is int && 
             config['columns'] is int;
    } catch (e) {
      return false;
    }
  }

  /// تحويل إلى نص للعرض
  String toDisplayText() {
    final buffer = StringBuffer();
    buffer.writeln('📊 $title');
    buffer.writeln('الصفوف: $rows | الأعمدة: $columns');

    final tableData = cellData;
    if (tableData.isNotEmpty) {
      // عرض أول صفين كمعاينة
      for (int i = 0; i < tableData.length && i < 2; i++) {
        buffer.writeln(tableData[i].join(' | '));
      }

      if (tableData.length > 2) {
        buffer.writeln('...');
      }
    }

    return buffer.toString();
  }

  /// الحصول على التنسيق الافتراضي
  static Map<String, dynamic> _getDefaultStyling() {
    return {
      'borderColor': '#E0E0E0',
      'headerBackgroundColor': '#F5F5F5',
      'cellPadding': 8.0,
      'fontSize': 14.0,
      'textAlign': 'right', // للعربية
      'borderWidth': 1.0,
      'alternateRowColor': '#FAFAFA',
    };
  }

  /// الحصول على بيانات جدول افتراضية
  static Map<String, dynamic> _getDefaultTableData() {
    return {
      'data': [
        ['العمود 1', 'العمود 2'],
        ['', ''],
      ],
      'rows': 2,
      'columns': 2,
      'hasHeader': true,
      'title': 'جدول البيانات',
      'styling': _getDefaultStyling(),
      'createdAt': DateTime.now().millisecondsSinceEpoch,
      'version': '1.0',
    };
  }

  @override
  String toString() => 'TableEmbed(${toDisplayText()})';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TableEmbed && other.data == data;
  }

  @override
  int get hashCode => data.hashCode;
}
