import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

/// خدمة الأمان المتقدمة
/// 
/// توفر ميزات أمان إضافية مثل:
/// - التشفير المتقدم
/// - حماية من الهجمات
/// - التحقق من سلامة البيانات
/// - مراقبة الأنشطة المشبوهة
class AdvancedSecurityService extends GetxService {
  static AdvancedSecurityService get instance => Get.find<AdvancedSecurityService>();

  // ===== إعدادات الأمان =====
  static const int _maxLoginAttempts = 5;
  static const int _lockoutDurationMinutes = 30;
  static const int _sessionTimeoutMinutes = 60;
  static const int _passwordMinLength = 6;

  // ===== متغيرات الحالة =====
  final RxMap<String, int> _loginAttempts = <String, int>{}.obs;
  final RxMap<String, DateTime> _lockedAccounts = <String, DateTime>{}.obs;
  final RxMap<String, DateTime> _activeSessions = <String, DateTime>{}.obs;
  final RxList<SecurityEvent> _securityEvents = <SecurityEvent>[].obs;

  @override
  void onInit() {
    super.onInit();
    debugPrint('🔒 تم تهيئة خدمة الأمان المتقدمة');
    _startSecurityMonitoring();
  }

  // ===== التشفير المتقدم =====

  /// تشفير النص باستخدام AES
  String encryptText(String plainText, String key) {
    try {
      final keyBytes = utf8.encode(key.padRight(32, '0').substring(0, 32));
      final plainBytes = utf8.encode(plainText);
      
      // إنشاء IV عشوائي
      final iv = _generateRandomBytes(16);
      
      // تشفير البيانات (محاكاة - في التطبيق الحقيقي استخدم مكتبة تشفير متقدمة)
      final encrypted = _xorEncrypt(plainBytes, keyBytes);
      
      // دمج IV مع البيانات المشفرة
      final combined = Uint8List.fromList([...iv, ...encrypted]);
      
      return base64.encode(combined);
    } catch (e) {
      debugPrint('❌ خطأ في التشفير: $e');
      throw SecurityException('فشل في تشفير البيانات');
    }
  }

  /// فك تشفير النص
  String decryptText(String encryptedText, String key) {
    try {
      final keyBytes = utf8.encode(key.padRight(32, '0').substring(0, 32));
      final combined = base64.decode(encryptedText);
      
      // استخراج IV والبيانات المشفرة
      // final iv = combined.sublist(0, 16); // غير مستخدم حالياً
      final encrypted = combined.sublist(16);
      
      // فك التشفير
      final decrypted = _xorEncrypt(encrypted, keyBytes);
      
      return utf8.decode(decrypted);
    } catch (e) {
      debugPrint('❌ خطأ في فك التشفير: $e');
      throw SecurityException('فشل في فك تشفير البيانات');
    }
  }

  /// تشفير كلمة المرور بـ Hash متقدم
  String hashPassword(String password, {String? salt}) {
    salt ??= _generateSalt();

    // استخدام hash بسيط للمحاكاة (في التطبيق الحقيقي استخدم crypto package)
    final combined = password + salt;
    var hash = combined.hashCode.toString();

    // تطبيق عدة جولات من الـ hashing
    for (int i = 0; i < 1000; i++) {
      hash = (hash + combined).hashCode.toString();
    }

    return '$salt:${base64.encode(utf8.encode(hash))}';
  }

  /// التحقق من كلمة المرور
  bool verifyPassword(String password, String hashedPassword) {
    try {
      final parts = hashedPassword.split(':');
      if (parts.length != 2) return false;
      
      final salt = parts[0];
      // final hash = parts[1]; // غير مستخدم - سيتم المقارنة مع hashedPassword مباشرة

      final newHash = hashPassword(password, salt: salt);
      return newHash == hashedPassword;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من كلمة المرور: $e');
      return false;
    }
  }

  // ===== حماية من الهجمات =====

  /// التحقق من محاولات تسجيل الدخول
  bool checkLoginAttempts(String identifier) {
    // التحقق من القفل
    if (_lockedAccounts.containsKey(identifier)) {
      final lockTime = _lockedAccounts[identifier]!;
      if (DateTime.now().difference(lockTime).inMinutes < _lockoutDurationMinutes) {
        _logSecurityEvent(SecurityEventType.accountLocked, identifier);
        return false;
      } else {
        // انتهت فترة القفل
        _lockedAccounts.remove(identifier);
        _loginAttempts.remove(identifier);
      }
    }

    return true;
  }

  /// تسجيل محاولة تسجيل دخول فاشلة
  void recordFailedLogin(String identifier) {
    final attempts = _loginAttempts[identifier] ?? 0;
    _loginAttempts[identifier] = attempts + 1;

    if (_loginAttempts[identifier]! >= _maxLoginAttempts) {
      _lockedAccounts[identifier] = DateTime.now();
      _logSecurityEvent(SecurityEventType.accountLocked, identifier);
      debugPrint('🔒 تم قفل الحساب $identifier لمدة $_lockoutDurationMinutes دقيقة');
    } else {
      _logSecurityEvent(SecurityEventType.failedLogin, identifier);
    }
  }

  /// تسجيل تسجيل دخول ناجح
  void recordSuccessfulLogin(String identifier) {
    _loginAttempts.remove(identifier);
    _lockedAccounts.remove(identifier);
    _activeSessions[identifier] = DateTime.now();
    _logSecurityEvent(SecurityEventType.successfulLogin, identifier);
  }

  /// التحقق من صحة الجلسة
  bool isSessionValid(String identifier) {
    if (!_activeSessions.containsKey(identifier)) return false;
    
    final sessionTime = _activeSessions[identifier]!;
    final isValid = DateTime.now().difference(sessionTime).inMinutes < _sessionTimeoutMinutes;
    
    if (!isValid) {
      _activeSessions.remove(identifier);
      _logSecurityEvent(SecurityEventType.sessionExpired, identifier);
    }
    
    return isValid;
  }

  /// تحديث وقت الجلسة
  void updateSessionTime(String identifier) {
    if (_activeSessions.containsKey(identifier)) {
      _activeSessions[identifier] = DateTime.now();
    }
  }

  /// إنهاء الجلسة
  void endSession(String identifier) {
    _activeSessions.remove(identifier);
    _logSecurityEvent(SecurityEventType.logout, identifier);
  }

  // ===== التحقق من سلامة البيانات =====

  /// التحقق من قوة كلمة المرور
  PasswordStrength checkPasswordStrength(String password) {
    if (password.length < _passwordMinLength) {
      return PasswordStrength.weak;
    }

    int score = 0;
    
    // طول كلمة المرور
    if (password.length >= 12) score += 2;
    else if (password.length >= 8) score += 1;
    
    // أحرف كبيرة
    if (password.contains(RegExp(r'[A-Z]'))) score += 1;
    
    // أحرف صغيرة
    if (password.contains(RegExp(r'[a-z]'))) score += 1;
    
    // أرقام
    if (password.contains(RegExp(r'[0-9]'))) score += 1;
    
    // رموز خاصة
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score += 2;
    
    // تنوع الأحرف
    if (password.length > 0 && password.split('').toSet().length / password.length > 0.7) {
      score += 1;
    }

    if (score >= 7) return PasswordStrength.strong;
    if (score >= 4) return PasswordStrength.medium;
    return PasswordStrength.weak;
  }

  /// التحقق من البيانات المشبوهة
  bool detectSuspiciousData(String data) {
    // فحص SQL Injection
    final sqlPatterns = [
      r"('|(\\')|(;|\\;)|(\\|)|(\\*)|(\*|\\*))",
      r"(union|select|insert|delete|update|drop|create|alter)",
      r"(script|javascript|vbscript|onload|onerror)",
    ];

    for (final pattern in sqlPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(data)) {
        _logSecurityEvent(SecurityEventType.suspiciousData, data);
        return true;
      }
    }

    return false;
  }

  // ===== مراقبة الأمان =====

  /// بدء مراقبة الأمان
  void _startSecurityMonitoring() {
    // تنظيف البيانات القديمة كل ساعة
    Timer.periodic(const Duration(hours: 1), (timer) {
      _cleanupOldData();
    });
  }

  /// تنظيف البيانات القديمة
  void _cleanupOldData() {
    final now = DateTime.now();
    
    // إزالة الأقفال المنتهية الصلاحية
    _lockedAccounts.removeWhere((key, value) => 
      now.difference(value).inMinutes >= _lockoutDurationMinutes);
    
    // إزالة الجلسات المنتهية الصلاحية
    _activeSessions.removeWhere((key, value) => 
      now.difference(value).inMinutes >= _sessionTimeoutMinutes);
    
    // الاحتفاظ بآخر 1000 حدث أمان فقط
    if (_securityEvents.length > 1000) {
      _securityEvents.removeRange(0, _securityEvents.length - 1000);
    }
    
    debugPrint('🧹 تم تنظيف بيانات الأمان القديمة');
  }

  /// تسجيل حدث أمان
  void _logSecurityEvent(SecurityEventType type, String details) {
    final event = SecurityEvent(
      type: type,
      details: details,
      timestamp: DateTime.now(),
      ipAddress: 'Unknown', // في التطبيق الحقيقي، احصل على IP الحقيقي
    );
    
    _securityEvents.add(event);
    debugPrint('🔍 حدث أمان: ${type.name} - $details');
  }

  // ===== طرق مساعدة =====

  /// إنشاء bytes عشوائية
  Uint8List _generateRandomBytes(int length) {
    final random = Random.secure();
    return Uint8List.fromList(List.generate(length, (i) => random.nextInt(256)));
  }

  /// إنشاء salt عشوائي
  String _generateSalt() {
    final random = Random.secure();
    final bytes = List.generate(16, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }

  /// تشفير XOR بسيط (للمحاكاة)
  Uint8List _xorEncrypt(List<int> data, List<int> key) {
    final result = <int>[];
    for (int i = 0; i < data.length; i++) {
      result.add(data[i] ^ key[i % key.length]);
    }
    return Uint8List.fromList(result);
  }

  // ===== Getters =====
  List<SecurityEvent> get securityEvents => _securityEvents.toList();
  Map<String, int> get loginAttempts => Map.from(_loginAttempts);
  Map<String, DateTime> get lockedAccounts => Map.from(_lockedAccounts);
  Map<String, DateTime> get activeSessions => Map.from(_activeSessions);
}

/// أنواع أحداث الأمان
enum SecurityEventType {
  successfulLogin,
  failedLogin,
  accountLocked,
  sessionExpired,
  logout,
  suspiciousData,
  unauthorizedAccess,
}

/// قوة كلمة المرور
enum PasswordStrength {
  weak,
  medium,
  strong,
}

/// حدث أمان
class SecurityEvent {
  final SecurityEventType type;
  final String details;
  final DateTime timestamp;
  final String ipAddress;

  SecurityEvent({
    required this.type,
    required this.details,
    required this.timestamp,
    required this.ipAddress,
  });

  Map<String, dynamic> toJson() => {
    'type': type.name,
    'details': details,
    'timestamp': timestamp.toIso8601String(),
    'ipAddress': ipAddress,
  };
}

/// استثناء أمان
class SecurityException implements Exception {
  final String message;
  SecurityException(this.message);
  
  @override
  String toString() => 'SecurityException: $message';
}
